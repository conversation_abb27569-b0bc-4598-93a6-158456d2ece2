/**
 * 马里奥角色类
 * 处理马里奥的状态、动画和行为
 */
export class Mario {
    constructor(x, y, gameEngine) {
        this.gameEngine = gameEngine;
        this.physicsBody = null;
        
        // 位置和尺寸
        this.x = x;
        this.y = y;
        this.baseWidth = 16;
        this.baseHeight = 16;
        
        // 状态
        this.state = 'small'; // 'small', 'big', 'fire'
        this.facing = 'right';
        this.onGround = false;
        this.invulnerable = false;
        this.invulnerabilityTime = 0;
        this.dead = false;
        
        // 物理属性
        this.maxSpeed = 200; // 像素/秒
        this.acceleration = 800;
        this.jumpSpeed = 350;
        this.runMultiplier = 1.5;
        this.friction = 0.85;
        
        // 动画系统
        this.animations = new Map();
        this.currentAnimation = 'idle';
        this.animationFrame = 0;
        this.animationTime = 0;
        this.animationSpeed = 8; // 帧/秒
        
        // 能力
        this.canShootFire = false;
        this.fireballCooldown = 0;
        this.maxFireballs = 2;
        this.activeFireballs = 0;
        
        // 输入缓冲
        this.jumpBuffer = 0;
        this.jumpBufferTime = 0.1;
        
        // 土狼时间（离开平台后仍可跳跃的时间）
        this.coyoteTime = 0;
        this.coyoteTimeMax = 0.15;
        
        // 初始化
        this.initializeAnimations();
        this.createPhysicsBody();
        
        console.log(`马里奥角色创建完成 - 位置: (${this.x}, ${this.y}), 物理体: (${this.physicsBody?.x}, ${this.physicsBody?.y})`);
    }

    /**
     * 初始化动画
     */
    initializeAnimations() {
        // 小马里奥动画
        this.animations.set('small_idle', {
            frames: [0],
            duration: 1.0
        });
        
        this.animations.set('small_walk', {
            frames: [1, 2, 3],
            duration: 0.3
        });
        
        this.animations.set('small_jump', {
            frames: [4],
            duration: 1.0
        });
        
        this.animations.set('small_duck', {
            frames: [5],
            duration: 1.0
        });
        
        // 大马里奥动画
        this.animations.set('big_idle', {
            frames: [0],
            duration: 1.0
        });
        
        this.animations.set('big_walk', {
            frames: [1, 2, 3],
            duration: 0.3
        });
        
        this.animations.set('big_jump', {
            frames: [4],
            duration: 1.0
        });
        
        this.animations.set('big_duck', {
            frames: [5],
            duration: 1.0
        });
        
        // 火焰马里奥动画
        this.animations.set('fire_idle', {
            frames: [0],
            duration: 1.0
        });
        
        this.animations.set('fire_walk', {
            frames: [1, 2, 3],
            duration: 0.3
        });
        
        this.animations.set('fire_jump', {
            frames: [4],
            duration: 1.0
        });
        
        this.animations.set('fire_duck', {
            frames: [5],
            duration: 1.0
        });
        
        // 变身动画
        this.animations.set('grow', {
            frames: [0, 6, 0, 6, 0],
            duration: 1.0
        });
        
        this.animations.set('shrink', {
            frames: [6, 0, 6, 0, 6],
            duration: 1.0
        });
        
        // 死亡动画
        this.animations.set('death', {
            frames: [7],
            duration: 2.0
        });
    }

    /**
     * 创建物理体
     */
    createPhysicsBody() {
        const physics = this.gameEngine.physicsSystem;
        
        this.physicsBody = physics.createBody('mario', {
            x: this.x,
            y: this.y,
            width: this.baseWidth,
            height: this.getHeight(),
            mass: 1,
            friction: this.friction,
            velocityX: 0,
            velocityY: 0,
            accelerationX: 0,
            accelerationY: 0,
            layer: 'player',
            mask: ['solid', 'enemy', 'item', 'trigger'],
            onCollision: (other) => this.onCollision(other),
            onTrigger: (other) => this.onTrigger(other)
        });
        
        console.log(`马里奥物理体创建 - 位置: (${this.physicsBody.x}, ${this.physicsBody.y}), 速度: (${this.physicsBody.velocityX}, ${this.physicsBody.velocityY})`);
    }

    /**
     * 获取当前高度（根据状态）
     */
    getHeight() {
        return this.state === 'small' ? this.baseHeight : this.baseHeight * 2;
    }

    /**
     * 获取当前纹理名称
     */
    getTextureName() {
        const prefix = this.state === 'fire' ? 'mario-fire' : 
                      this.state === 'big' ? 'mario-big' : 'mario-small';
        return prefix;
    }

    /**
     * 更新马里奥
     */
    update(deltaTime) {
        if (this.dead) {
            this.updateDeathAnimation(deltaTime);
            return;
        }
        
        // 更新无敌时间
        if (this.invulnerable) {
            this.invulnerabilityTime -= deltaTime;
            if (this.invulnerabilityTime <= 0) {
                this.invulnerable = false;
            }
        }
        
        // 更新火球冷却
        if (this.fireballCooldown > 0) {
            this.fireballCooldown -= deltaTime;
        }
        
        // 更新跳跃缓冲
        if (this.jumpBuffer > 0) {
            this.jumpBuffer -= deltaTime;
        }
        
        // 更新土狼时间
        if (!this.onGround && this.coyoteTime > 0) {
            this.coyoteTime -= deltaTime;
        }
        
        // 处理输入
        this.handleInput(deltaTime);
        
        // 更新物理位置
        this.updatePhysics(deltaTime);
        
        // 更新动画
        this.updateAnimation(deltaTime);
        
        // 边界检查
        this.checkBoundaries();
    }

    /**
     * 处理输入
     */
    handleInput(deltaTime) {
        const input = this.gameEngine.inputManager;
        const physics = this.gameEngine.physicsSystem;
        
        // 水平移动
        let moveInput = 0;
        if (input.isActionPressed('move_left')) {
            moveInput = -1;
            this.facing = 'left';
        } else if (input.isActionPressed('move_right')) {
            moveInput = 1;
            this.facing = 'right';
        }
        
        // 计算目标速度
        const runPressed = input.isActionPressed('run');
        const maxSpeed = this.maxSpeed * (runPressed ? this.runMultiplier : 1);
        const targetVelocityX = moveInput * maxSpeed;
        
        // 应用加速度
        if (moveInput !== 0) {
            const force = this.acceleration * moveInput;
            physics.applyForce('mario', force, 0);
            
            // 限制速度
            if (this.physicsBody) {
                if (Math.abs(this.physicsBody.velocityX) > maxSpeed) {
                    this.physicsBody.velocityX = Math.sign(this.physicsBody.velocityX) * maxSpeed;
                }
            }
        }
        
        // 跳跃输入缓冲
        if (input.isActionJustPressed('jump')) {
            this.jumpBuffer = this.jumpBufferTime;
        }
        
        // 跳跃逻辑
        if (this.jumpBuffer > 0 && (this.onGround || this.coyoteTime > 0)) {
            this.jump();
            this.jumpBuffer = 0;
            this.coyoteTime = 0;
        }
        
        // 变长跳跃（松开跳跃键时减少上升速度）
        if (input.isActionJustReleased('jump') && this.physicsBody && this.physicsBody.velocityY < 0) {
            this.physicsBody.velocityY *= 0.5;
        }
        
        // 蹲下
        const ducking = input.isActionPressed('duck') && this.onGround;
        
        // 发射火球
        if (input.isActionJustPressed('fire') && this.canShootFire && this.fireballCooldown <= 0) {
            this.shootFireball();
        }
        
        // 更新动画状态
        this.updateAnimationState(moveInput, ducking);
    }

    /**
     * 跳跃
     */
    jump() {
        if (this.physicsBody) {
            this.physicsBody.velocityY = -this.jumpSpeed;
            this.onGround = false;
            
            // 播放跳跃音效
            this.gameEngine.audioManager.playSound('jump');
        }
    }

    /**
     * 发射火球
     */
    shootFireball() {
        if (this.activeFireballs >= this.maxFireballs) return;
        
        // 创建火球实体（这里简化处理）
        const fireballX = this.x + (this.facing === 'right' ? this.baseWidth : 0);
        const fireballY = this.y + this.getHeight() / 2;
        const velocityX = this.facing === 'right' ? 300 : -300;
        
        // TODO: 创建实际的火球实体
        console.log(`发射火球: x=${fireballX}, y=${fireballY}, vx=${velocityX}`);
        
        this.activeFireballs++;
        this.fireballCooldown = 0.3;
        
        // 播放音效
        this.gameEngine.audioManager.playSound('fireball');
    }

    /**
     * 更新动画状态
     */
    updateAnimationState(moveInput, ducking) {
        let newAnimation = this.currentAnimation;
        
        if (ducking && this.state !== 'small') {
            newAnimation = `${this.state}_duck`;
        } else if (!this.onGround) {
            newAnimation = `${this.state}_jump`;
        } else if (Math.abs(moveInput) > 0) {
            newAnimation = `${this.state}_walk`;
        } else {
            newAnimation = `${this.state}_idle`;
        }
        
        this.setAnimation(newAnimation);
    }

    /**
     * 更新物理
     */
    updatePhysics(deltaTime) {
        if (this.physicsBody) {
            // 记录之前的位置以进行调试
            const prevY = this.y;
            
            // 同步物理体位置到马里奥
            this.x = this.physicsBody.x;
            this.y = this.physicsBody.y;
            
            // 调试：如果位置发生大幅变化，输出日志
            if (Math.abs(this.y - prevY) > 50) {
                console.log(`马里奥位置大幅变化: ${prevY} -> ${this.y}, 速度Y: ${this.physicsBody.velocityY}, onGround: ${this.physicsBody.onGround}`);
            }
            
            // 检查是否在地面
            const wasOnGround = this.onGround;
            this.onGround = this.physicsBody.onGround;
            
            // 如果刚落地，重置土狼时间
            if (!wasOnGround && this.onGround) {
                this.coyoteTime = this.coyoteTimeMax;
            } else if (wasOnGround && !this.onGround) {
                this.coyoteTime = this.coyoteTimeMax;
            }
        }
    }

    /**
     * 更新动画
     */
    updateAnimation(deltaTime) {
        const animation = this.animations.get(this.currentAnimation);
        if (!animation) return;
        
        this.animationTime += deltaTime;
        const frameTime = animation.duration / animation.frames.length;
        
        if (this.animationTime >= frameTime) {
            this.animationTime = 0;
            this.animationFrame = (this.animationFrame + 1) % animation.frames.length;
        }
    }

    /**
     * 设置动画
     */
    setAnimation(animationName) {
        if (this.currentAnimation !== animationName && this.animations.has(animationName)) {
            this.currentAnimation = animationName;
            this.animationFrame = 0;
            this.animationTime = 0;
        }
    }

    /**
     * 边界检查
     */
    checkBoundaries() {
        // 给物理系统时间稳定，避免在初始化时误判死亡
        if (!this.gameEngine.initializationComplete || this.gameEngine.initializationTime < 1.0) {
            return;
        }
        
        // 检查是否掉出世界
        if (this.y > this.gameEngine.levelHeight + 100) {
            console.log(`马里奥位置: y=${this.y}, 死亡线: ${this.gameEngine.levelHeight + 100}`);
            this.die();
        }
        
        // 左边界限制
        if (this.x < 0) {
            this.x = 0;
            if (this.physicsBody) {
                this.physicsBody.x = 0;
                this.physicsBody.velocityX = Math.max(0, this.physicsBody.velocityX);
            }
        }
    }

    /**
     * 碰撞处理
     */
    onCollision(other) {
        switch (other.layer) {
            case 'enemy':
                this.handleEnemyCollision(other);
                break;
            case 'item':
                this.handleItemCollision(other);
                break;
        }
    }

    /**
     * 触发器处理
     */
    onTrigger(other) {
        switch (other.layer) {
            case 'checkpoint':
                this.setCheckpoint(other.x, other.y);
                break;
            case 'levelend':
                this.gameEngine.emit('levelComplete');
                break;
        }
    }

    /**
     * 处理敌人碰撞
     */
    handleEnemyCollision(enemy) {
        if (this.invulnerable) return;
        
        // 检查是否是从上方踩踏
        const marioBottom = this.y + this.getHeight();
        const enemyTop = enemy.y;
        
        if (marioBottom - 5 <= enemyTop && this.physicsBody.velocityY > 0) {
            // 踩踏敌人
            this.stompEnemy(enemy);
        } else {
            // 被敌人伤害
            this.takeDamage();
        }
    }

    /**
     * 踩踏敌人
     */
    stompEnemy(enemy) {
        // 弹跳效果
        if (this.physicsBody) {
            this.physicsBody.velocityY = -200;
        }
        
        // 播放踩踏音效
        this.gameEngine.audioManager.playSound('stomp');
        
        // 增加分数
        this.gameEngine.gameState.score += 100;
        
        // TODO: 销毁敌人或改变其状态
        console.log('踩踏敌人:', enemy.id);
    }

    /**
     * 处理道具碰撞
     */
    handleItemCollision(item) {
        switch (item.type) {
            case 'coin':
                this.collectCoin();
                break;
            case 'mushroom':
                this.powerUp('big');
                break;
            case 'fireflower':
                this.powerUp('fire');
                break;
            case '1up':
                this.gainLife();
                break;
        }
        
        // TODO: 销毁道具
        console.log('收集道具:', item.type);
    }

    /**
     * 收集金币
     */
    collectCoin() {
        this.gameEngine.gameState.coins++;
        this.gameEngine.gameState.score += 200;
        
        // 播放金币音效
        this.gameEngine.audioManager.playSound('coin');
        
        // 检查是否获得生命
        if (this.gameEngine.gameState.coins >= 100) {
            this.gameEngine.gameState.coins -= 100;
            this.gainLife();
        }
    }

    /**
     * 升级
     */
    powerUp(newState) {
        if (this.state === newState) return;
        
        const oldState = this.state;
        this.state = newState;
        
        // 更新物理体大小
        if (this.physicsBody) {
            this.physicsBody.height = this.getHeight();
        }
        
        // 设置能力
        this.canShootFire = (newState === 'fire');
        
        // 播放升级动画和音效
        if (oldState === 'small' && newState === 'big') {
            this.setAnimation('grow');
            this.gameEngine.audioManager.playSound('powerup');
        } else if (newState === 'fire') {
            this.gameEngine.audioManager.playSound('powerup');
        }
        
        // 增加分数
        this.gameEngine.gameState.score += 1000;
        
        console.log(`马里奥升级: ${oldState} -> ${newState}`);
    }

    /**
     * 受到伤害
     */
    takeDamage() {
        if (this.invulnerable) return;
        
        if (this.state === 'small') {
            this.die();
        } else {
            // 降级
            const newState = this.state === 'fire' ? 'big' : 'small';
            this.state = newState;
            
            // 更新物理体大小
            if (this.physicsBody) {
                this.physicsBody.height = this.getHeight();
            }
            
            // 设置无敌时间
            this.invulnerable = true;
            this.invulnerabilityTime = 2.0;
            
            // 失去火焰能力
            this.canShootFire = false;
            
            // 播放降级动画和音效
            this.setAnimation('shrink');
            this.gameEngine.audioManager.playSound('hit');
            
            console.log(`马里奥降级到: ${newState}`);
        }
    }

    /**
     * 死亡
     */
    die() {
        if (this.dead) return;
        
        console.log(`马里奥死亡触发 - 当前位置: (${this.x}, ${this.y}), 死亡线: ${this.gameEngine.levelHeight + 100}, 初始化时间: ${this.gameEngine.initializationTime}`);
        console.trace('马里奥死亡调用栈:');
        
        this.dead = true;
        this.setAnimation('death');
        
        // 停止物理模拟
        if (this.physicsBody) {
            this.physicsBody.velocityX = 0;
            this.physicsBody.velocityY = -300; // 死亡跳跃
            this.physicsBody.applyGravity = true;
        }
        
        // 播放死亡音效
        this.gameEngine.audioManager.playSound('death');
        
        // 减少生命
        this.gameEngine.gameState.lives--;
        
        console.log('马里奥死亡');
        
        // 延迟触发游戏结束检查
        setTimeout(() => {
            this.gameEngine.emit('marioDied');
        }, 2000);
    }

    /**
     * 获得生命
     */
    gainLife() {
        this.gameEngine.gameState.lives++;
        this.gameEngine.audioManager.playSound('1up');
        console.log('获得1UP');
    }

    /**
     * 设置检查点
     */
    setCheckpoint(x, y) {
        this.checkpointX = x;
        this.checkpointY = y;
        console.log(`检查点设置: (${x}, ${y})`);
    }

    /**
     * 复活
     */
    respawn() {
        this.dead = false;
        this.state = 'small';
        this.canShootFire = false;
        this.invulnerable = true;
        this.invulnerabilityTime = 2.0;
        
        // 重置位置
        this.x = this.checkpointX || 100;
        this.y = this.checkpointY || 400;
        
        if (this.physicsBody) {
            this.physicsBody.x = this.x;
            this.physicsBody.y = this.y;
            this.physicsBody.velocityX = 0;
            this.physicsBody.velocityY = 0;
            this.physicsBody.height = this.getHeight();
            this.physicsBody.applyGravity = true;
        }
        
        this.setAnimation('small_idle');
        console.log('马里奥复活');
    }

    /**
     * 更新死亡动画
     */
    updateDeathAnimation(deltaTime) {
        // 死亡状态下的物理更新
        if (this.physicsBody) {
            this.physicsBody.velocityY += 800 * deltaTime; // 重力
            this.y += this.physicsBody.velocityY * deltaTime;
        }
    }

    /**
     * 渲染马里奥
     */
    render(spriteRenderer) {
        if (!spriteRenderer) {
            console.warn('没有spriteRenderer');
            return;
        }
        
        const texture = this.getTextureName();
        const animation = this.animations.get(this.currentAnimation);
        
        // 计算纹理区域（如果使用精灵图集）
        let texRegion = null;
        if (animation) {
            const frame = animation.frames[this.animationFrame];
            // 这里需要根据实际的纹理图集计算纹理区域
            texRegion = this.getTextureRegion(texture, frame);
        }
        
        // 无敌时的闪烁效果
        const alpha = this.invulnerable && 
                     Math.floor(this.invulnerabilityTime * 10) % 2 === 0 ? 0.5 : 1.0;
        
        // 只在第一次渲染时输出调试信息
        if (!this.renderDebugLogged) {
            console.log(`渲染马里奥: 纹理=${texture}, 位置=(${this.x}, ${this.y}), 大小=(${this.baseWidth}, ${this.getHeight()})`);
            this.renderDebugLogged = true;
        }
        
        spriteRenderer.drawSprite({
            texture: texture,
            x: this.x,
            y: this.y,
            width: this.baseWidth,
            height: this.getHeight(),
            color: [1, 1, 1, alpha],
            texRegion: texRegion,
            flipX: this.facing === 'left'
        });
    }

    /**
     * 获取纹理区域
     */
    getTextureRegion(textureName, frame) {
        // 这里应该根据实际的纹理图集返回相应的区域
        // 暂时返回null，使用整个纹理
        return null;
    }

    /**
     * 获取马里奥状态
     */
    getState() {
        return {
            x: this.x,
            y: this.y,
            state: this.state,
            facing: this.facing,
            onGround: this.onGround,
            dead: this.dead,
            invulnerable: this.invulnerable,
            canShootFire: this.canShootFire,
            currentAnimation: this.currentAnimation,
            animationFrame: this.animationFrame
        };
    }

    /**
     * 释放资源
     */
    dispose() {
        if (this.physicsBody) {
            this.gameEngine.physicsSystem.removeBody('mario');
            this.physicsBody = null;
        }
        
        this.animations.clear();
        console.log('马里奥角色已释放');
    }
}