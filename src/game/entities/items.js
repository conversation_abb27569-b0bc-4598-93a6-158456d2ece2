/**
 * 道具管理器和道具类
 * 处理各种道具的行为、动画和收集逻辑
 */

/**
 * 基础道具类
 */
class Item {
    constructor(x, y, type, gameEngine) {
        this.gameEngine = gameEngine;
        this.type = type;
        this.x = x;
        this.y = y;
        this.width = 16;
        this.height = 16;
        
        // 状态
        this.collected = false;
        this.active = true;
        this.visible = true;
        
        // 物理属性
        this.velocityX = 0;
        this.velocityY = 0;
        this.gravity = 400;
        this.bounceHeight = 0;
        this.floating = false;
        
        // 动画
        this.animationFrame = 0;
        this.animationTime = 0;
        this.animationSpeed = 4;
        
        // 效果
        this.bobOffset = 0;
        this.bobSpeed = 3;
        this.sparkleTime = 0;
        
        // 物理体
        this.physicsBody = null;
        
        // 创建物理体
        this.createPhysicsBody();
        
        // 初始化道具特定属性
        this.initialize();
    }

    /**
     * 初始化道具特定属性
     */
    initialize() {
        // 子类重写
    }

    /**
     * 创建物理体
     */
    createPhysicsBody() {
        const physics = this.gameEngine.physicsSystem;
        const id = `item_${this.type}_${Date.now()}_${Math.random()}`;
        
        this.physicsBody = physics.createBody(id, {
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height,
            mass: 0.3,
            isTrigger: true, // 道具是触发器，不会物理阻挡
            layer: 'item',
            mask: ['player'],
            onTrigger: (other) => this.onTrigger(other),
            applyGravity: !this.floating,
            type: this.type
        });
    }

    /**
     * 更新道具
     */
    update(deltaTime) {
        if (this.collected || !this.active) return;
        
        // 更新动画
        this.updateAnimation(deltaTime);
        
        // 更新物理
        this.updatePhysics(deltaTime);
        
        // 更新效果
        this.updateEffects(deltaTime);
        
        // 边界检查
        this.checkBoundaries();
    }

    /**
     * 更新动画
     */
    updateAnimation(deltaTime) {
        this.animationTime += deltaTime;
        if (this.animationTime >= 1 / this.animationSpeed) {
            this.animationTime = 0;
            this.animationFrame = (this.animationFrame + 1) % this.getAnimationFrameCount();
        }
    }

    /**
     * 获取动画帧数
     */
    getAnimationFrameCount() {
        return 4; // 默认4帧动画
    }

    /**
     * 更新物理
     */
    updatePhysics(deltaTime) {
        if (this.physicsBody) {
            // 同步位置
            this.x = this.physicsBody.x;
            this.y = this.physicsBody.y;
            
            // 应用水平速度
            if (this.velocityX !== 0) {
                this.physicsBody.velocityX = this.velocityX;
            }
        }
    }

    /**
     * 更新效果
     */
    updateEffects(deltaTime) {
        // 浮动效果
        this.bobOffset = Math.sin(this.gameEngine.clock.totalTime * this.bobSpeed) * 2;
        
        // 闪烁效果
        this.sparkleTime += deltaTime;
    }

    /**
     * 边界检查
     */
    checkBoundaries() {
        // 如果掉出世界，销毁道具
        if (this.y > this.gameEngine.levelHeight + 100) {
            this.destroy();
        }
    }

    /**
     * 触发器处理
     */
    onTrigger(other) {
        if (other.layer === 'player' && !this.collected) {
            this.collect();
        }
    }

    /**
     * 收集道具
     */
    collect() {
        if (this.collected) return;
        
        this.collected = true;
        this.active = false;
        
        // 播放收集音效
        this.playCollectSound();
        
        // 应用道具效果
        this.applyEffect();
        
        // 增加分数
        this.addScore();
        
        // 销毁道具
        this.destroy();
    }

    /**
     * 播放收集音效
     */
    playCollectSound() {
        // 子类重写
    }

    /**
     * 应用道具效果
     */
    applyEffect() {
        // 子类重写
    }

    /**
     * 增加分数
     */
    addScore() {
        this.gameEngine.gameState.score += 100;
    }

    /**
     * 销毁道具
     */
    destroy() {
        this.collected = true;
        this.active = false;
        
        if (this.physicsBody) {
            this.gameEngine.physicsSystem.removeBody(this.physicsBody.id);
            this.physicsBody = null;
        }
    }

    /**
     * 渲染道具
     */
    render(spriteRenderer) {
        if (!this.active || this.collected || !this.visible || !spriteRenderer) return;
        
        const renderY = this.y + this.bobOffset;
        
        spriteRenderer.drawSprite({
            texture: this.type,
            x: this.x,
            y: renderY,
            width: this.width,
            height: this.height,
            color: this.getColor()
        });
        
        // 渲染闪烁效果
        if (this.shouldSparkle()) {
            this.renderSparkle(spriteRenderer, renderY);
        }
    }

    /**
     * 获取颜色（用于特殊效果）
     */
    getColor() {
        return [1, 1, 1, 1]; // 默认白色
    }

    /**
     * 是否应该闪烁
     */
    shouldSparkle() {
        return this.sparkleTime > 0 && Math.floor(this.sparkleTime * 10) % 2 === 0;
    }

    /**
     * 渲染闪烁效果
     */
    renderSparkle(spriteRenderer, y) {
        // 简单的闪烁效果
        spriteRenderer.drawSprite({
            texture: '__white',
            x: this.x - 2,
            y: y - 2,
            width: this.width + 4,
            height: this.height + 4,
            color: [1, 1, 1, 0.3]
        });
    }
}

/**
 * 金币类
 */
class Coin extends Item {
    constructor(x, y, gameEngine) {
        super(x, y, 'coin', gameEngine);
        this.floating = true; // 金币悬浮
        this.value = 200;
    }

    initialize() {
        // 金币不受重力影响
        if (this.physicsBody) {
            this.physicsBody.applyGravity = false;
        }
    }

    playCollectSound() {
        this.gameEngine.audioManager.playSound('coin');
    }

    applyEffect() {
        // 增加金币数量
        this.gameEngine.gameState.coins++;
        
        // 检查是否获得生命
        if (this.gameEngine.gameState.coins >= 100) {
            this.gameEngine.gameState.coins -= 100;
            this.gameEngine.gameState.lives++;
            this.gameEngine.audioManager.playSound('1up');
        }
    }

    addScore() {
        this.gameEngine.gameState.score += this.value;
    }
}

/**
 * 蘑菇类（变大）
 */
class Mushroom extends Item {
    constructor(x, y, gameEngine) {
        super(x, y, 'mushroom', gameEngine);
        this.velocityX = 60; // 蘑菇会移动
        this.emerged = false;
        this.emergingTime = 0;
        this.emergingDuration = 0.5;
    }

    initialize() {
        // 蘑菇从砖块中出现
        this.y -= this.height; // 开始时隐藏在砖块中
        this.visible = false;
        this.startEmerging();
    }

    startEmerging() {
        this.emerging = true;
        this.emergingTime = 0;
    }

    update(deltaTime) {
        if (this.emerging) {
            this.updateEmerging(deltaTime);
        } else {
            super.update(deltaTime);
        }
    }

    updateEmerging(deltaTime) {
        this.emergingTime += deltaTime;
        const progress = this.emergingTime / this.emergingDuration;
        
        if (progress >= 1.0) {
            this.emerging = false;
            this.emerged = true;
            this.visible = true;
            // 激活物理体
            if (this.physicsBody) {
                this.physicsBody.applyGravity = true;
            }
        } else {
            // 渐渐上升
            const targetY = this.y + this.height;
            this.y = targetY - this.height * progress;
            this.visible = progress > 0.2; // 部分出现后才可见
        }
    }

    updatePhysics(deltaTime) {
        super.updatePhysics(deltaTime);
        
        // 遇到障碍物时转向
        if (this.emerged && this.physicsBody) {
            // 检查前方是否有障碍
            const direction = this.velocityX > 0 ? 1 : -1;
            // 这里可以添加射线检测来检查前方障碍
        }
    }

    playCollectSound() {
        this.gameEngine.audioManager.playSound('powerup');
    }

    applyEffect() {
        // 让马里奥变大
        const mario = this.gameEngine.mario;
        if (mario) {
            mario.powerUp('big');
        }
    }

    addScore() {
        this.gameEngine.gameState.score += 1000;
    }
}

/**
 * 火花类
 */
class FireFlower extends Item {
    constructor(x, y, gameEngine) {
        super(x, y, 'fireflower', gameEngine);
        this.floating = true;
        this.colorTime = 0;
    }

    initialize() {
        if (this.physicsBody) {
            this.physicsBody.applyGravity = false;
        }
    }

    updateEffects(deltaTime) {
        super.updateEffects(deltaTime);
        this.colorTime += deltaTime;
    }

    getColor() {
        // 火花会变色
        const r = 0.8 + 0.2 * Math.sin(this.colorTime * 6);
        const g = 0.6 + 0.2 * Math.sin(this.colorTime * 6 + Math.PI / 3);
        const b = 0.4;
        return [r, g, b, 1];
    }

    playCollectSound() {
        this.gameEngine.audioManager.playSound('powerup');
    }

    applyEffect() {
        const mario = this.gameEngine.mario;
        if (mario) {
            mario.powerUp('fire');
        }
    }

    addScore() {
        this.gameEngine.gameState.score += 1000;
    }
}

/**
 * 1UP蘑菇类
 */
class OneUpMushroom extends Item {
    constructor(x, y, gameEngine) {
        super(x, y, '1up', gameEngine);
        this.velocityX = 50;
    }

    getColor() {
        // 绿色的1UP蘑菇
        return [0.5, 1, 0.5, 1];
    }

    playCollectSound() {
        this.gameEngine.audioManager.playSound('1up');
    }

    applyEffect() {
        this.gameEngine.gameState.lives++;
    }

    addScore() {
        // 1UP不增加分数，但很珍贵
    }
}

/**
 * 星星类（无敌）
 */
class Star extends Item {
    constructor(x, y, gameEngine) {
        super(x, y, 'star', gameEngine);
        this.velocityX = 100;
        this.bounceHeight = -200;
    }

    updatePhysics(deltaTime) {
        super.updatePhysics(deltaTime);
        
        // 星星会弹跳
        if (this.physicsBody && this.physicsBody.onGround) {
            this.physicsBody.velocityY = this.bounceHeight;
        }
    }

    getColor() {
        // 彩虹色效果
        const time = this.gameEngine.clock.totalTime;
        const r = 0.5 + 0.5 * Math.sin(time * 10);
        const g = 0.5 + 0.5 * Math.sin(time * 10 + Math.PI * 2 / 3);
        const b = 0.5 + 0.5 * Math.sin(time * 10 + Math.PI * 4 / 3);
        return [r, g, b, 1];
    }

    playCollectSound() {
        this.gameEngine.audioManager.playSound('powerup');
    }

    applyEffect() {
        const mario = this.gameEngine.mario;
        if (mario) {
            mario.makeInvincible(10); // 10秒无敌
        }
    }

    addScore() {
        this.gameEngine.gameState.score += 1000;
    }
}

/**
 * 道具管理器
 */
export class ItemManager {
    constructor(gameEngine) {
        this.gameEngine = gameEngine;
        this.items = new Map();
        this.itemClasses = new Map();
        
        // 注册道具类型
        this.itemClasses.set('coin', Coin);
        this.itemClasses.set('mushroom', Mushroom);
        this.itemClasses.set('fireflower', FireFlower);
        this.itemClasses.set('1up', OneUpMushroom);
        this.itemClasses.set('star', Star);
        
        console.log('道具管理器创建完成');
    }

    /**
     * 创建道具
     */
    createItem(type, x, y, options = {}) {
        const ItemClass = this.itemClasses.get(type);
        if (!ItemClass) {
            console.warn(`未知的道具类型: ${type}`);
            return null;
        }
        
        const item = new ItemClass(x, y, this.gameEngine);
        const id = `item_${type}_${Date.now()}_${Math.random()}`;
        
        // 应用选项
        Object.assign(item, options);
        
        this.items.set(id, item);
        
        console.log(`创建道具: ${type} at (${x}, ${y})`);
        return item;
    }

    /**
     * 从砖块中生成道具
     */
    spawnFromBlock(blockType, x, y) {
        let itemType = 'coin'; // 默认金币
        
        // 根据砖块类型和马里奥状态决定道具类型
        const mario = this.gameEngine.mario;
        
        if (blockType === 'question') {
            if (mario.state === 'small') {
                itemType = 'mushroom';
            } else {
                itemType = 'fireflower';
            }
        } else if (blockType === 'brick') {
            // 普通砖块可能包含金币或1UP
            itemType = Math.random() < 0.9 ? 'coin' : '1up';
        }
        
        return this.createItem(itemType, x, y);
    }

    /**
     * 移除道具
     */
    removeItem(id) {
        const item = this.items.get(id);
        if (item) {
            item.destroy();
            this.items.delete(id);
        }
    }

    /**
     * 获取道具
     */
    getItem(id) {
        return this.items.get(id);
    }

    /**
     * 获取所有道具
     */
    getAllItems() {
        return Array.from(this.items.values());
    }

    /**
     * 获取指定范围内的道具
     */
    getItemsInRange(x, y, range) {
        const nearbyItems = [];
        
        for (const item of this.items.values()) {
            const distance = Math.sqrt(
                (item.x - x) ** 2 + (item.y - y) ** 2
            );
            
            if (distance <= range) {
                nearbyItems.push(item);
            }
        }
        
        return nearbyItems;
    }

    /**
     * 更新所有道具
     */
    update(deltaTime) {
        // 清理已收集的道具
        for (const [id, item] of this.items) {
            if (item.collected || !item.active) {
                this.items.delete(id);
                continue;
            }
            
            item.update(deltaTime);
        }
    }

    /**
     * 渲染所有道具
     */
    render(spriteRenderer) {
        for (const item of this.items.values()) {
            if (item.active && !item.collected) {
                item.render(spriteRenderer);
            }
        }
    }

    /**
     * 从关卡数据加载道具
     */
    loadFromLevel(levelData) {
        if (!levelData.items) return;
        
        for (const itemData of levelData.items) {
            this.createItem(itemData.type, itemData.x, itemData.y, itemData.options || {});
        }
        
        console.log(`从关卡加载了 ${levelData.items.length} 个道具`);
    }

    /**
     * 清理所有道具
     */
    clear() {
        for (const item of this.items.values()) {
            item.destroy();
        }
        this.items.clear();
    }

    /**
     * 注册自定义道具类型
     */
    registerItemType(type, ItemClass) {
        this.itemClasses.set(type, ItemClass);
    }

    /**
     * 获取道具数量
     */
    getItemCount() {
        return this.items.size;
    }

    /**
     * 按类型获取道具数量
     */
    getItemCountByType(type) {
        let count = 0;
        for (const item of this.items.values()) {
            if (item.type === type) {
                count++;
            }
        }
        return count;
    }

    /**
     * 获取统计信息
     */
    getStats() {
        const stats = {
            total: this.items.size,
            active: 0,
            collected: 0,
            byType: new Map()
        };
        
        for (const item of this.items.values()) {
            if (item.active) stats.active++;
            if (item.collected) stats.collected++;
            
            const count = stats.byType.get(item.type) || 0;
            stats.byType.set(item.type, count + 1);
        }
        
        return stats;
    }

    /**
     * 释放资源
     */
    dispose() {
        this.clear();
        this.itemClasses.clear();
        console.log('道具管理器已释放资源');
    }
}