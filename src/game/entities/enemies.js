/**
 * 敌人管理器和敌人类
 * 处理各种敌人的AI、行为和渲染
 */

/**
 * 基础敌人类
 */
class Enemy {
    constructor(x, y, type, gameEngine) {
        this.gameEngine = gameEngine;
        this.type = type;
        this.x = x;
        this.y = y;
        this.width = 16;
        this.height = 16;
        
        // 状态
        this.alive = true;
        this.active = true;
        this.facing = 'left';
        this.onGround = false;
        
        // 物理属性
        this.velocityX = -50; // 默认向左移动
        this.velocityY = 0;
        this.maxSpeed = 100;
        this.health = 1;
        
        // 行为状态
        this.behaviorState = 'patrol';
        this.behaviorTimer = 0;
        
        // 动画
        this.animationFrame = 0;
        this.animationTime = 0;
        this.animationSpeed = 4;
        
        // 物理体
        this.physicsBody = null;
        
        // 创建物理体
        this.createPhysicsBody();
    }

    /**
     * 创建物理体
     */
    createPhysicsBody() {
        const physics = this.gameEngine.physicsSystem;
        const id = `enemy_${this.type}_${Date.now()}_${Math.random()}`;
        
        this.physicsBody = physics.createBody(id, {
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height,
            mass: 0.5,
            friction: 0.3,
            layer: 'enemy',
            mask: ['solid', 'player'],
            onCollision: (other) => this.onCollision(other),
            type: this.type
        });
    }

    /**
     * 更新敌人
     */
    update(deltaTime) {
        if (!this.alive || !this.active) return;
        
        // 更新行为
        this.updateBehavior(deltaTime);
        
        // 更新物理
        this.updatePhysics(deltaTime);
        
        // 更新动画
        this.updateAnimation(deltaTime);
        
        // 边界检查
        this.checkBoundaries();
    }

    /**
     * 更新行为
     */
    updateBehavior(deltaTime) {
        // 子类重写
    }

    /**
     * 更新物理
     */
    updatePhysics(deltaTime) {
        if (this.physicsBody) {
            // 同步位置
            this.x = this.physicsBody.x;
            this.y = this.physicsBody.y;
            this.onGround = this.physicsBody.onGround;
            
            // 应用水平速度
            this.physicsBody.velocityX = this.velocityX;
        }
    }

    /**
     * 更新动画
     */
    updateAnimation(deltaTime) {
        this.animationTime += deltaTime;
        if (this.animationTime >= 1 / this.animationSpeed) {
            this.animationTime = 0;
            this.animationFrame = (this.animationFrame + 1) % this.getAnimationFrameCount();
        }
    }

    /**
     * 获取动画帧数
     */
    getAnimationFrameCount() {
        return 2; // 默认2帧动画
    }

    /**
     * 边界检查
     */
    checkBoundaries() {
        // 如果掉出世界，销毁敌人
        if (this.y > this.gameEngine.levelHeight + 100) {
            this.destroy();
        }
    }

    /**
     * 碰撞处理
     */
    onCollision(other) {
        if (other.layer === 'solid') {
            this.handleSolidCollision(other);
        } else if (other.layer === 'player') {
            this.handlePlayerCollision(other);
        }
    }

    /**
     * 处理与固体的碰撞
     */
    handleSolidCollision(solid) {
        // 检查是否是墙壁碰撞，需要转向
        const enemyCenter = this.x + this.width / 2;
        const solidCenter = solid.x + solid.width / 2;
        
        if (Math.abs(enemyCenter - solidCenter) > Math.abs(this.y - solid.y)) {
            // 水平碰撞，转向
            this.turn();
        }
    }

    /**
     * 处理与玩家的碰撞
     */
    handlePlayerCollision(player) {
        // 在马里奥类中处理
    }

    /**
     * 转向
     */
    turn() {
        this.velocityX = -this.velocityX;
        this.facing = this.velocityX > 0 ? 'right' : 'left';
    }

    /**
     * 被踩踏
     */
    stomp() {
        this.destroy();
        
        // 增加分数
        this.gameEngine.gameState.score += 100;
        
        // 播放音效
        this.gameEngine.audioManager.playSound('stomp');
    }

    /**
     * 受到伤害
     */
    takeDamage(damage = 1) {
        this.health -= damage;
        if (this.health <= 0) {
            this.destroy();
        }
    }

    /**
     * 销毁敌人
     */
    destroy() {
        this.alive = false;
        this.active = false;
        
        if (this.physicsBody) {
            this.gameEngine.physicsSystem.removeBody(this.physicsBody.id);
            this.physicsBody = null;
        }
    }

    /**
     * 渲染敌人
     */
    render(spriteRenderer) {
        if (!this.alive || !spriteRenderer) return;
        
        spriteRenderer.drawSprite({
            texture: this.type,
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height,
            flipX: this.facing === 'right'
        });
    }
}

/**
 * 蘑菇怪(Goomba)敌人
 */
class Goomba extends Enemy {
    constructor(x, y, gameEngine) {
        super(x, y, 'goomba', gameEngine);
        
        this.velocityX = -30; // 慢速移动
        this.width = 16;
        this.height = 16;
        this.squashed = false;
        this.squashTime = 0;
    }

    updateBehavior(deltaTime) {
        if (this.squashed) {
            this.squashTime += deltaTime;
            if (this.squashTime >= 0.5) {
                this.destroy();
            }
            return;
        }

        // 简单的巡逻行为
        this.behaviorTimer += deltaTime;
        
        // 检查前方是否有悬崖或墙壁
        if (this.behaviorTimer >= 0.1) {
            this.checkForObstacles();
            this.behaviorTimer = 0;
        }
    }

    checkForObstacles() {
        const physics = this.gameEngine.physicsSystem;
        const checkDistance = 20;
        const direction = this.facing === 'left' ? -1 : 1;
        
        // 检查前方地面
        const groundCheck = physics.raycast(
            this.x + this.width / 2,
            this.y + this.height,
            this.x + this.width / 2 + direction * checkDistance,
            this.y + this.height + 10,
            ['solid']
        );
        
        // 如果前方没有地面，转向
        if (groundCheck.length === 0) {
            this.turn();
        }
    }

    stomp() {
        if (this.squashed) return;
        
        this.squashed = true;
        this.velocityX = 0;
        this.height = 8; // 被压扁
        
        if (this.physicsBody) {
            this.physicsBody.height = 8;
            this.physicsBody.velocityX = 0;
        }
        
        super.stomp();
    }

    render(spriteRenderer) {
        if (!this.alive || !spriteRenderer) return;
        
        const texture = this.squashed ? 'goomba_squashed' : 'goomba';
        
        spriteRenderer.drawSprite({
            texture: texture,
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height,
            flipX: this.facing === 'right'
        });
    }
}

/**
 * 龟类(Koopa)敌人
 */
class Koopa extends Enemy {
    constructor(x, y, gameEngine) {
        super(x, y, 'koopa', gameEngine);
        
        this.velocityX = -40;
        this.width = 16;
        this.height = 24;
        this.inShell = false;
        this.shellVelocity = 0;
        this.shellTime = 0;
        this.canExit = false;
    }

    updateBehavior(deltaTime) {
        if (this.inShell) {
            this.updateShellBehavior(deltaTime);
        } else {
            // 正常巡逻
            this.behaviorTimer += deltaTime;
            if (this.behaviorTimer >= 0.1) {
                this.checkForObstacles();
                this.behaviorTimer = 0;
            }
        }
    }

    updateShellBehavior(deltaTime) {
        this.shellTime += deltaTime;
        
        if (Math.abs(this.shellVelocity) > 0) {
            // 壳在移动
            this.velocityX = this.shellVelocity;
        } else {
            // 壳静止，准备重新出来
            this.velocityX = 0;
            
            if (this.shellTime >= 3.0) {
                this.canExit = true;
                // 闪烁提示即将出来
            }
            
            if (this.shellTime >= 5.0) {
                this.exitShell();
            }
        }
    }

    checkForObstacles() {
        // 与Goomba类似的障碍检查
        const physics = this.gameEngine.physicsSystem;
        const checkDistance = 20;
        const direction = this.facing === 'left' ? -1 : 1;
        
        const groundCheck = physics.raycast(
            this.x + this.width / 2,
            this.y + this.height,
            this.x + this.width / 2 + direction * checkDistance,
            this.y + this.height + 10,
            ['solid']
        );
        
        if (groundCheck.length === 0) {
            this.turn();
        }
    }

    stomp() {
        if (this.inShell) {
            // 踢壳
            this.kickShell();
        } else {
            // 进入壳状态
            this.enterShell();
        }
        
        super.stomp();
    }

    enterShell() {
        this.inShell = true;
        this.velocityX = 0;
        this.shellVelocity = 0;
        this.height = 16;
        this.shellTime = 0;
        this.canExit = false;
        
        if (this.physicsBody) {
            this.physicsBody.height = 16;
        }
    }

    kickShell() {
        // 根据马里奥的位置决定踢的方向
        const mario = this.gameEngine.mario;
        if (mario) {
            const direction = mario.x < this.x ? 1 : -1;
            this.shellVelocity = direction * 200;
            this.facing = direction > 0 ? 'right' : 'left';
        }
    }

    exitShell() {
        this.inShell = false;
        this.velocityX = this.facing === 'left' ? -40 : 40;
        this.height = 24;
        this.shellVelocity = 0;
        this.shellTime = 0;
        this.canExit = false;
        
        if (this.physicsBody) {
            this.physicsBody.height = 24;
        }
    }

    handleSolidCollision(solid) {
        super.handleSolidCollision(solid);
        
        // 如果在壳状态且有速度，碰到墙就反弹
        if (this.inShell && Math.abs(this.shellVelocity) > 0) {
            this.shellVelocity = -this.shellVelocity;
            this.facing = this.shellVelocity > 0 ? 'right' : 'left';
        }
    }

    render(spriteRenderer) {
        if (!this.alive || !spriteRenderer) return;
        
        let texture = 'koopa';
        if (this.inShell) {
            texture = this.canExit && Math.floor(this.shellTime * 8) % 2 ? 'koopa_shell_flash' : 'koopa_shell';
        }
        
        spriteRenderer.drawSprite({
            texture: texture,
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height,
            flipX: this.facing === 'right'
        });
    }
}

/**
 * 敌人管理器
 */
export class EnemyManager {
    constructor(gameEngine) {
        this.gameEngine = gameEngine;
        this.enemies = new Map();
        this.enemyClasses = new Map();
        
        // 注册敌人类型
        this.enemyClasses.set('goomba', Goomba);
        this.enemyClasses.set('koopa', Koopa);
        
        console.log('敌人管理器创建完成');
    }

    /**
     * 创建敌人
     */
    createEnemy(type, x, y) {
        const EnemyClass = this.enemyClasses.get(type);
        if (!EnemyClass) {
            console.warn(`未知的敌人类型: ${type}`);
            return null;
        }
        
        const enemy = new EnemyClass(x, y, this.gameEngine);
        const id = `enemy_${type}_${Date.now()}_${Math.random()}`;
        
        this.enemies.set(id, enemy);
        
        console.log(`创建敌人: ${type} at (${x}, ${y})`);
        return enemy;
    }

    /**
     * 移除敌人
     */
    removeEnemy(id) {
        const enemy = this.enemies.get(id);
        if (enemy) {
            enemy.destroy();
            this.enemies.delete(id);
        }
    }

    /**
     * 获取敌人
     */
    getEnemy(id) {
        return this.enemies.get(id);
    }

    /**
     * 获取所有敌人
     */
    getAllEnemies() {
        return Array.from(this.enemies.values());
    }

    /**
     * 获取指定范围内的敌人
     */
    getEnemiesInRange(x, y, range) {
        const nearbyEnemies = [];
        
        for (const enemy of this.enemies.values()) {
            const distance = Math.sqrt(
                (enemy.x - x) ** 2 + (enemy.y - y) ** 2
            );
            
            if (distance <= range) {
                nearbyEnemies.push(enemy);
            }
        }
        
        return nearbyEnemies;
    }

    /**
     * 更新所有敌人
     */
    update(deltaTime) {
        // 清理死亡的敌人
        for (const [id, enemy] of this.enemies) {
            if (!enemy.alive) {
                this.enemies.delete(id);
                continue;
            }
            
            enemy.update(deltaTime);
        }
    }

    /**
     * 渲染所有敌人
     */
    render(spriteRenderer) {
        for (const enemy of this.enemies.values()) {
            if (enemy.alive) {
                enemy.render(spriteRenderer);
            }
        }
    }

    /**
     * 从关卡数据加载敌人
     */
    loadFromLevel(levelData) {
        if (!levelData.enemies) return;
        
        for (const enemyData of levelData.enemies) {
            this.createEnemy(enemyData.type, enemyData.x, enemyData.y);
        }
        
        console.log(`从关卡加载了 ${levelData.enemies.length} 个敌人`);
    }

    /**
     * 清理所有敌人
     */
    clear() {
        for (const enemy of this.enemies.values()) {
            enemy.destroy();
        }
        this.enemies.clear();
    }

    /**
     * 注册自定义敌人类型
     */
    registerEnemyType(type, EnemyClass) {
        this.enemyClasses.set(type, EnemyClass);
    }

    /**
     * 获取敌人数量
     */
    getEnemyCount() {
        return this.enemies.size;
    }

    /**
     * 获取统计信息
     */
    getStats() {
        const stats = {
            total: this.enemies.size,
            alive: 0,
            active: 0,
            byType: new Map()
        };
        
        for (const enemy of this.enemies.values()) {
            if (enemy.alive) stats.alive++;
            if (enemy.active) stats.active++;
            
            const count = stats.byType.get(enemy.type) || 0;
            stats.byType.set(enemy.type, count + 1);
        }
        
        return stats;
    }

    /**
     * 释放资源
     */
    dispose() {
        this.clear();
        this.enemyClasses.clear();
        console.log('敌人管理器已释放资源');
    }
}