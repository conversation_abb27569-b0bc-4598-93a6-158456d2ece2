/**
 * 关卡系统
 * 处理关卡加载、瓦片地图和背景渲染
 */
export class Level {
    constructor(gameEngine) {
        this.gameEngine = gameEngine;
        
        // 关卡基本信息
        this.id = '';
        this.name = '';
        this.width = 3200;
        this.height = 480;
        
        // 瓦片地图
        this.tileSize = 16;
        this.tilesWidth = 0;
        this.tilesHeight = 0;
        this.tiles = [];
        
        // 背景
        this.background = {
            color: [0.53, 0.81, 0.98, 1.0], // 天空蓝
            layers: []
        };
        
        // 游戏对象
        this.platforms = [];
        this.decorations = [];
        
        // 特殊区域
        this.checkpoints = [];
        this.secretAreas = [];
        this.warpZones = [];
        
        // 关卡边界
        this.bounds = {
            left: 0,
            right: 3200,
            top: 0,
            bottom: 480
        };
        
        // 滚动设置
        this.scrollType = 'horizontal'; // 'horizontal', 'vertical', 'free'
        this.autoScroll = false;
        this.scrollSpeed = 0;
        
        // 物理体缓存
        this.staticBodies = new Map();
        
        console.log('关卡系统创建完成');
    }

    /**
     * 加载关卡数据
     */
    async loadLevel(levelId) {
        try {
            // 这里应该从文件加载，暂时使用内置数据
            const levelData = this.getLevelData(levelId);
            
            this.id = levelData.id;
            this.name = levelData.name || levelId;
            this.width = levelData.width || 3200;
            this.height = levelData.height || 480;
            
            // 更新边界
            this.bounds.right = this.width;
            this.bounds.bottom = this.height;
            
            // 加载背景
            this.loadBackground(levelData.background);
            
            // 加载瓦片地图
            this.loadTileMap(levelData.tileMap);
            
            // 加载平台
            this.loadPlatforms(levelData.platforms);
            
            // 加载装饰
            this.loadDecorations(levelData.decorations);
            
            // 加载特殊区域
            this.loadSpecialAreas(levelData);
            
            // 通知其他系统加载关卡内容
            this.gameEngine.enemyManager.loadFromLevel(levelData);
            this.gameEngine.itemManager.loadFromLevel(levelData);
            
            console.log(`关卡 ${levelId} 加载完成`);
            
        } catch (error) {
            console.error(`加载关卡 ${levelId} 失败:`, error);
            throw error;
        }
    }

    /**
     * 获取关卡数据
     */
    getLevelData(levelId) {
        // 预定义的关卡数据
        const levels = {
            '1-1': {
                id: '1-1',
                name: '世界1-1',
                width: 3200,
                height: 480,
                background: {
                    color: [0.53, 0.81, 0.98, 1.0],
                    layers: [
                        {
                            texture: 'cloud',
                            y: 80,
                            scrollSpeed: 0.3,
                            repeat: true
                        },
                        {
                            texture: 'bush',
                            y: 380,
                            scrollSpeed: 0.6,
                            repeat: true
                        }
                    ]
                },
                platforms: [
                    // 地面
                    { x: 0, y: 432, width: 2000, height: 48, texture: 'ground', type: 'solid' },
                    { x: 2048, y: 432, width: 1152, height: 48, texture: 'ground', type: 'solid' },
                    
                    // 悬浮平台
                    { x: 512, y: 352, width: 64, height: 16, texture: 'brick', type: 'solid' },
                    { x: 608, y: 352, width: 16, height: 16, texture: 'question', type: 'question' },
                    { x: 656, y: 352, width: 48, height: 16, texture: 'brick', type: 'solid' },
                    
                    { x: 768, y: 288, width: 16, height: 16, texture: 'question', type: 'question' },
                    { x: 832, y: 288, width: 32, height: 16, texture: 'brick', type: 'brick' },
                    { x: 896, y: 288, width: 16, height: 16, texture: 'question', type: 'question' },
                    
                    // 阶梯
                    { x: 1024, y: 416, width: 16, height: 16, texture: 'brick', type: 'solid' },
                    { x: 1040, y: 400, width: 16, height: 32, texture: 'brick', type: 'solid' },
                    { x: 1056, y: 384, width: 16, height: 48, texture: 'brick', type: 'solid' },
                    { x: 1072, y: 368, width: 16, height: 64, texture: 'brick', type: 'solid' },
                    
                    // 管道
                    { x: 448, y: 400, width: 32, height: 32, texture: 'pipe', type: 'pipe', warpTo: 'underground' },
                    { x: 2624, y: 368, width: 32, height: 64, texture: 'pipe', type: 'pipe' },
                    
                    // 城堡
                    { x: 3104, y: 336, width: 80, height: 96, texture: 'castle', type: 'decoration' }
                ],
                decorations: [
                    // 山丘
                    { x: 384, y: 400, texture: 'hill_small', type: 'background' },
                    { x: 944, y: 384, texture: 'hill_large', type: 'background' },
                    { x: 1536, y: 400, texture: 'hill_small', type: 'background' },
                    
                    // 云朵
                    { x: 152, y: 120, texture: 'cloud_small', type: 'background' },
                    { x: 424, y: 88, texture: 'cloud_large', type: 'background' },
                    { x: 680, y: 104, texture: 'cloud_medium', type: 'background' },
                    { x: 936, y: 136, texture: 'cloud_small', type: 'background' },
                    
                    // 灌木
                    { x: 304, y: 416, texture: 'bush_small', type: 'background' },
                    { x: 864, y: 416, texture: 'bush_large', type: 'background' },
                    { x: 1200, y: 416, texture: 'bush_medium', type: 'background' }
                ],
                enemies: [
                    { type: 'goomba', x: 256, y: 400 },
                    { type: 'goomba', x: 352, y: 400 },
                    { type: 'goomba', x: 1728, y: 400 },
                    { type: 'goomba', x: 1776, y: 400 },
                    { type: 'koopa', x: 1472, y: 400 },
                    { type: 'goomba', x: 2240, y: 400 },
                    { type: 'koopa', x: 2512, y: 400 }
                ],
                items: [
                    { type: 'coin', x: 600, y: 320 },
                    { type: 'coin', x: 640, y: 320 },
                    { type: 'coin', x: 680, y: 320 }
                ],
                checkpoints: [
                    { x: 1600, y: 400 }
                ],
                goalPost: { x: 3008, y: 144 }
            }
        };
        
        const levelData = levels[levelId];
        if (!levelData) {
            throw new Error(`关卡数据未找到: ${levelId}`);
        }
        
        return levelData;
    }

    /**
     * 加载背景
     */
    loadBackground(backgroundData) {
        if (!backgroundData) return;
        
        this.background = {
            color: backgroundData.color || [0.53, 0.81, 0.98, 1.0],
            layers: backgroundData.layers || []
        };
    }

    /**
     * 加载瓦片地图
     */
    loadTileMap(tileMapData) {
        if (!tileMapData) {
            // 创建空的瓦片地图
            this.tilesWidth = Math.ceil(this.width / this.tileSize);
            this.tilesHeight = Math.ceil(this.height / this.tileSize);
            this.tiles = new Array(this.tilesWidth * this.tilesHeight).fill(0);
            return;
        }
        
        this.tileSize = tileMapData.tileSize || 16;
        this.tilesWidth = tileMapData.width || Math.ceil(this.width / this.tileSize);
        this.tilesHeight = tileMapData.height || Math.ceil(this.height / this.tileSize);
        this.tiles = tileMapData.data || new Array(this.tilesWidth * this.tilesHeight).fill(0);
    }

    /**
     * 加载平台
     */
    loadPlatforms(platformsData) {
        if (!platformsData) return;
        
        this.platforms = [];
        const physics = this.gameEngine.physicsSystem;
        
        for (const platform of platformsData) {
            this.platforms.push(platform);
            
            // 创建物理体
            if (platform.type === 'solid' || platform.type === 'brick' || platform.type === 'question') {
                const bodyId = `platform_${this.platforms.length - 1}`;
                const body = physics.createBody(bodyId, {
                    x: platform.x,
                    y: platform.y,
                    width: platform.width,
                    height: platform.height,
                    isStatic: true,
                    layer: 'solid',
                    mask: ['player', 'enemy'],
                    type: platform.type,
                    texture: platform.texture
                });
                
                this.staticBodies.set(bodyId, body);
            }
        }
        
        console.log(`加载了 ${this.platforms.length} 个平台`);
    }

    /**
     * 加载装饰
     */
    loadDecorations(decorationsData) {
        if (!decorationsData) return;
        
        this.decorations = decorationsData || [];
        console.log(`加载了 ${this.decorations.length} 个装饰`);
    }

    /**
     * 加载特殊区域
     */
    loadSpecialAreas(levelData) {
        // 加载检查点
        this.checkpoints = levelData.checkpoints || [];
        for (const checkpoint of this.checkpoints) {
            const physics = this.gameEngine.physicsSystem;
            physics.createBody(`checkpoint_${checkpoint.x}_${checkpoint.y}`, {
                x: checkpoint.x,
                y: checkpoint.y,
                width: 32,
                height: 32,
                isStatic: true,
                isTrigger: true,
                layer: 'checkpoint',
                mask: ['player']
            });
        }
        
        // 加载终点
        if (levelData.goalPost) {
            const goal = levelData.goalPost;
            const physics = this.gameEngine.physicsSystem;
            physics.createBody('goalpost', {
                x: goal.x,
                y: goal.y,
                width: 16,
                height: 336,
                isStatic: true,
                isTrigger: true,
                layer: 'levelend',
                mask: ['player']
            });
        }
    }

    /**
     * 获取指定位置的瓦片
     */
    getTileAt(x, y) {
        const tileX = Math.floor(x / this.tileSize);
        const tileY = Math.floor(y / this.tileSize);
        
        if (tileX < 0 || tileX >= this.tilesWidth || tileY < 0 || tileY >= this.tilesHeight) {
            return 0; // 边界外返回0
        }
        
        return this.tiles[tileY * this.tilesWidth + tileX];
    }

    /**
     * 设置指定位置的瓦片
     */
    setTileAt(x, y, tileId) {
        const tileX = Math.floor(x / this.tileSize);
        const tileY = Math.floor(y / this.tileSize);
        
        if (tileX < 0 || tileX >= this.tilesWidth || tileY < 0 || tileY >= this.tilesHeight) {
            return false;
        }
        
        this.tiles[tileY * this.tilesWidth + tileX] = tileId;
        return true;
    }

    /**
     * 检查指定位置是否是固体瓦片
     */
    isSolidAt(x, y) {
        const tile = this.getTileAt(x, y);
        // 0 = 空气，1+ = 固体
        return tile > 0;
    }

    /**
     * 破坏砖块
     */
    breakBrick(x, y) {
        // 找到对应的平台
        for (let i = 0; i < this.platforms.length; i++) {
            const platform = this.platforms[i];
            
            if (x >= platform.x && x < platform.x + platform.width &&
                y >= platform.y && y < platform.y + platform.height &&
                platform.type === 'brick') {
                
                // 移除物理体
                const bodyId = `platform_${i}`;
                const body = this.staticBodies.get(bodyId);
                if (body) {
                    this.gameEngine.physicsSystem.removeBody(bodyId);
                    this.staticBodies.delete(bodyId);
                }
                
                // 从平台列表移除
                this.platforms.splice(i, 1);
                
                // 播放破坏音效
                this.gameEngine.audioManager.playSound('break');
                
                // TODO: 创建破坏粒子效果
                
                return true;
            }
        }
        
        return false;
    }

    /**
     * 激活问号块
     */
    activateQuestionBlock(x, y) {
        for (let i = 0; i < this.platforms.length; i++) {
            const platform = this.platforms[i];
            
            if (x >= platform.x && x < platform.x + platform.width &&
                y >= platform.y && y < platform.y + platform.height &&
                platform.type === 'question') {
                
                // 改变为普通砖块
                platform.type = 'brick';
                platform.texture = 'brick_used';
                
                // 生成道具
                this.gameEngine.itemManager.spawnFromBlock('question', platform.x, platform.y - 16);
                
                // 播放音效
                this.gameEngine.audioManager.playSound('powerup');
                
                return true;
            }
        }
        
        return false;
    }

    /**
     * 更新关卡
     */
    update(deltaTime) {
        // 更新背景层动画
        this.updateBackgroundLayers(deltaTime);
        
        // 如果是自动滚动关卡，更新滚动
        if (this.autoScroll) {
            this.updateAutoScroll(deltaTime);
        }
        
        // 检查关卡边界
        this.enforceBoundaries();
    }

    /**
     * 更新背景层
     */
    updateBackgroundLayers(deltaTime) {
        for (const layer of this.background.layers) {
            if (layer.animate) {
                layer.animationTime = (layer.animationTime || 0) + deltaTime;
            }
        }
    }

    /**
     * 更新自动滚动
     */
    updateAutoScroll(deltaTime) {
        const camera = this.gameEngine.camera;
        camera.targetX += this.scrollSpeed * deltaTime;
        
        // 确保不超出边界
        camera.targetX = Math.max(this.bounds.left, 
                         Math.min(camera.targetX, this.bounds.right - this.gameEngine.canvas.width));
    }

    /**
     * 强制边界限制
     */
    enforceBoundaries() {
        const mario = this.gameEngine.mario;
        if (!mario) return;
        
        // 水平边界
        if (mario.x < this.bounds.left) {
            mario.x = this.bounds.left;
            if (mario.physicsBody) {
                mario.physicsBody.x = this.bounds.left;
                mario.physicsBody.velocityX = Math.max(0, mario.physicsBody.velocityX);
            }
        } else if (mario.x > this.bounds.right - mario.width) {
            mario.x = this.bounds.right - mario.width;
            if (mario.physicsBody) {
                mario.physicsBody.x = this.bounds.right - mario.width;
                mario.physicsBody.velocityX = Math.min(0, mario.physicsBody.velocityX);
            }
        }
        
        // 垂直边界（死亡检查在Mario类中处理）
    }

    /**
     * 渲染关卡
     */
    render(spriteRenderer) {
        if (!spriteRenderer) return;
        
        // 渲染背景
        this.renderBackground(spriteRenderer);
        
        // 渲染装饰（背景层）
        this.renderDecorations(spriteRenderer, 'background');
        
        // 渲染平台
        this.renderPlatforms(spriteRenderer);
        
        // 渲染装饰（前景层）
        this.renderDecorations(spriteRenderer, 'foreground');
        
        // 渲染特殊元素
        this.renderSpecialElements(spriteRenderer);
    }

    /**
     * 渲染背景
     */
    renderBackground(spriteRenderer) {
        const camera = this.gameEngine.camera;
        
        // 设置背景色
        this.gameEngine.renderer.setClearColor(...this.background.color);
        
        // 渲染背景层
        for (const layer of this.background.layers) {
            if (!layer.texture) continue;
            
            const parallaxX = camera.x * layer.scrollSpeed;
            const startX = Math.floor((camera.x - parallaxX) / 64) * 64 + parallaxX;
            const endX = camera.x + this.gameEngine.canvas.width + 64;
            
            for (let x = startX; x <= endX; x += 64) {
                spriteRenderer.drawSprite({
                    texture: layer.texture,
                    x: x,
                    y: layer.y,
                    width: 64,
                    height: 32
                });
            }
        }
    }

    /**
     * 渲染平台
     */
    renderPlatforms(spriteRenderer) {
        const camera = this.gameEngine.camera;
        const screenLeft = camera.x;
        const screenRight = camera.x + this.gameEngine.canvas.width;
        
        for (const platform of this.platforms) {
            // 裁剪检查
            if (platform.x + platform.width < screenLeft || platform.x > screenRight) {
                continue;
            }
            
            // 对于大平台，使用瓦片渲染
            if (platform.width > 16 || platform.height > 16) {
                this.renderTiledPlatform(spriteRenderer, platform);
            } else {
                spriteRenderer.drawSprite({
                    texture: platform.texture,
                    x: platform.x,
                    y: platform.y,
                    width: platform.width,
                    height: platform.height
                });
            }
        }
    }

    /**
     * 渲染瓦片化平台
     */
    renderTiledPlatform(spriteRenderer, platform) {
        const tilesX = Math.ceil(platform.width / 16);
        const tilesY = Math.ceil(platform.height / 16);
        
        for (let ty = 0; ty < tilesY; ty++) {
            for (let tx = 0; tx < tilesX; tx++) {
                let texture = platform.texture;
                
                // 根据位置选择不同的瓦片纹理
                if (platform.type === 'ground') {
                    if (ty === 0) {
                        texture = tx === 0 ? 'ground_top_left' :
                                tx === tilesX - 1 ? 'ground_top_right' : 'ground_top';
                    } else {
                        texture = tx === 0 ? 'ground_left' :
                                tx === tilesX - 1 ? 'ground_right' : 'ground_fill';
                    }
                }
                
                spriteRenderer.drawSprite({
                    texture: texture,
                    x: platform.x + tx * 16,
                    y: platform.y + ty * 16,
                    width: 16,
                    height: 16
                });
            }
        }
    }

    /**
     * 渲染装饰
     */
    renderDecorations(spriteRenderer, layer) {
        const camera = this.gameEngine.camera;
        const screenLeft = camera.x;
        const screenRight = camera.x + this.gameEngine.canvas.width;
        
        for (const decoration of this.decorations) {
            if (decoration.type !== layer) continue;
            
            // 裁剪检查
            if (decoration.x + 64 < screenLeft || decoration.x > screenRight) {
                continue;
            }
            
            spriteRenderer.drawSprite({
                texture: decoration.texture,
                x: decoration.x,
                y: decoration.y,
                width: 64,
                height: 32
            });
        }
    }

    /**
     * 渲染特殊元素
     */
    renderSpecialElements(spriteRenderer) {
        // 渲染终点旗杆
        const goalPost = this.getGoalPost();
        if (goalPost) {
            spriteRenderer.drawSprite({
                texture: 'flagpole',
                x: goalPost.x,
                y: goalPost.y,
                width: 16,
                height: 336
            });
        }
    }

    /**
     * 获取终点旗杆位置
     */
    getGoalPost() {
        for (const body of this.staticBodies.values()) {
            if (body.layer === 'levelend') {
                return { x: body.x, y: body.y };
            }
        }
        return null;
    }

    /**
     * 获取玩家出生点
     */
    getSpawnPoint() {
        return { x: 100, y: 400 };
    }

    /**
     * 获取关卡边界
     */
    getBounds() {
        return { ...this.bounds };
    }

    /**
     * 检查是否完成关卡
     */
    isComplete() {
        const mario = this.gameEngine.mario;
        const goalPost = this.getGoalPost();
        
        if (!mario || !goalPost) return false;
        
        return mario.x >= goalPost.x;
    }

    /**
     * 清理关卡
     */
    clear() {
        // 清理物理体
        for (const [bodyId, body] of this.staticBodies) {
            this.gameEngine.physicsSystem.removeBody(bodyId);
        }
        this.staticBodies.clear();
        
        // 清理数据
        this.platforms = [];
        this.decorations = [];
        this.checkpoints = [];
        this.tiles = [];
        
        console.log('关卡已清理');
    }

    /**
     * 释放资源
     */
    dispose() {
        this.clear();
        console.log('关卡系统已释放资源');
    }
}