/**
 * 物理系统
 * 处理碰撞检测、重力和基础物理模拟
 */
export class PhysicsSystem {
    constructor() {
        // 物理常量
        this.gravity = 800; // 像素/秒²
        this.friction = 0.8;
        this.airResistance = 0.99;
        
        // 碰撞组和层
        this.collisionLayers = new Map();
        this.collisionMatrix = new Map();
        
        // 物理实体
        this.bodies = new Map();
        this.staticBodies = new Map();
        
        // 空间分割优化
        this.spatialGrid = {
            cellSize: 64,
            grid: new Map()
        };
        
        // 性能统计
        this.stats = {
            bodiesCount: 0,
            collisionChecks: 0,
            collisions: 0
        };
        
        console.log('物理系统初始化完成');
    }

    /**
     * 创建物理体
     */
    createBody(id, config) {
        const body = {
            id,
            x: config.x || 0,
            y: config.y || 0,
            width: config.width || 32,
            height: config.height || 32,
            
            // 速度
            velocityX: config.velocityX || 0,
            velocityY: config.velocityY || 0,
            
            // 加速度
            accelerationX: config.accelerationX || 0,
            accelerationY: config.accelerationY || 0,
            
            // 物理属性
            mass: config.mass || 1,
            restitution: config.restitution || 0, // 弹性系数
            friction: config.friction || 0.5,
            
            // 标志
            isStatic: config.isStatic || false,
            isTrigger: config.isTrigger || false,
            applyGravity: config.applyGravity !== false,
            
            // 碰撞层
            layer: config.layer || 'default',
            mask: config.mask || ['default'],
            
            // 状态
            onGround: false,
            grounded: false,
            
            // 回调
            onCollision: config.onCollision || null,
            onTrigger: config.onTrigger || null,
            
            // AABB边界缓存
            bounds: {
                left: 0,
                right: 0,
                top: 0,
                bottom: 0
            }
        };
        
        this.updateBodyBounds(body);
        
        if (body.isStatic) {
            this.staticBodies.set(id, body);
        } else {
            this.bodies.set(id, body);
        }
        
        this.addToSpatialGrid(body);
        
        return body;
    }

    /**
     * 移除物理体
     */
    removeBody(id) {
        const body = this.bodies.get(id) || this.staticBodies.get(id);
        if (body) {
            this.removeFromSpatialGrid(body);
            this.bodies.delete(id);
            this.staticBodies.delete(id);
        }
    }

    /**
     * 获取物理体
     */
    getBody(id) {
        return this.bodies.get(id) || this.staticBodies.get(id);
    }

    /**
     * 更新物理系统
     */
    update(deltaTime) {
        this.stats.bodiesCount = this.bodies.size + this.staticBodies.size;
        this.stats.collisionChecks = 0;
        this.stats.collisions = 0;
        
        // 更新动态物理体
        for (const body of this.bodies.values()) {
            this.updateBody(body, deltaTime);
        }
        
        // 进行碰撞检测
        this.performCollisionDetection();
        
        // 更新空间网格
        this.updateSpatialGrid();
    }

    /**
     * 更新单个物理体
     */
    updateBody(body, deltaTime) {
        if (body.isStatic) return;
        
        // 限制deltaTime以防止数值爆炸
        deltaTime = Math.min(deltaTime, 1/30); // 最大33ms时间步长
        
        const prevX = body.x;
        const prevY = body.y;
        
        // 调试异常的deltaTime
        if (deltaTime > 0.1) {
            console.warn(`异常大的deltaTime: ${deltaTime}, 物体: ${body.id}`);
        }
        
        // 应用重力
        if (body.applyGravity) {
            body.accelerationY += this.gravity;
        }
        
        // 调试异常的加速度
        if (Math.abs(body.accelerationY) > 10000) {
            console.warn(`异常大的加速度Y: ${body.accelerationY}, 物体: ${body.id}`);
            body.accelerationY = Math.sign(body.accelerationY) * 1000; // 限制加速度
        }
        
        // 更新速度
        body.velocityX += body.accelerationX * deltaTime;
        body.velocityY += body.accelerationY * deltaTime;
        
        // 限制速度以防止数值爆炸
        const maxVelocity = 1000; // 最大速度限制
        body.velocityX = Math.max(-maxVelocity, Math.min(maxVelocity, body.velocityX));
        body.velocityY = Math.max(-maxVelocity, Math.min(maxVelocity, body.velocityY));
        
        // 应用空气阻力
        if (!body.onGround) {
            body.velocityX *= this.airResistance;
        }
        
        // 应用摩擦力（仅当在地面时）
        if (body.onGround) {
            body.velocityX *= (1 - body.friction);
        }
        
        // 更新位置
        body.x += body.velocityX * deltaTime;
        body.y += body.velocityY * deltaTime;
        
        // 更新边界
        this.updateBodyBounds(body);
        
        // 重置加速度
        body.accelerationX = 0;
        body.accelerationY = 0;
        
        // 重置地面状态
        body.onGround = false;
    }

    /**
     * 更新物理体边界
     */
    updateBodyBounds(body) {
        body.bounds.left = body.x;
        body.bounds.right = body.x + body.width;
        body.bounds.top = body.y;
        body.bounds.bottom = body.y + body.height;
    }

    /**
     * 执行碰撞检测
     */
    performCollisionDetection() {
        // 动态体与静态体碰撞
        for (const dynamicBody of this.bodies.values()) {
            const nearbyStatic = this.getNearbyBodies(dynamicBody, this.staticBodies);
            
            for (const staticBody of nearbyStatic) {
                if (this.canCollide(dynamicBody, staticBody)) {
                    this.checkCollision(dynamicBody, staticBody);
                }
            }
        }
        
        // 动态体之间碰撞
        const dynamicBodies = Array.from(this.bodies.values());
        for (let i = 0; i < dynamicBodies.length; i++) {
            for (let j = i + 1; j < dynamicBodies.length; j++) {
                const bodyA = dynamicBodies[i];
                const bodyB = dynamicBodies[j];
                
                if (this.canCollide(bodyA, bodyB)) {
                    this.checkCollision(bodyA, bodyB);
                }
            }
        }
    }

    /**
     * 检查两个物体是否可以碰撞
     */
    canCollide(bodyA, bodyB) {
        // 检查碰撞层
        return bodyA.mask.includes(bodyB.layer) || bodyB.mask.includes(bodyA.layer);
    }

    /**
     * 检查碰撞
     */
    checkCollision(bodyA, bodyB) {
        this.stats.collisionChecks++;
        
        if (this.aabbCollision(bodyA, bodyB)) {
            this.stats.collisions++;
            
            if (bodyA.isTrigger || bodyB.isTrigger) {
                this.handleTrigger(bodyA, bodyB);
            } else {
                this.resolveCollision(bodyA, bodyB);
            }
        }
    }

    /**
     * AABB碰撞检测
     */
    aabbCollision(bodyA, bodyB) {
        return (
            bodyA.bounds.left < bodyB.bounds.right &&
            bodyA.bounds.right > bodyB.bounds.left &&
            bodyA.bounds.top < bodyB.bounds.bottom &&
            bodyA.bounds.bottom > bodyB.bounds.top
        );
    }

    /**
     * 处理触发器
     */
    handleTrigger(bodyA, bodyB) {
        if (bodyA.isTrigger && bodyA.onTrigger) {
            bodyA.onTrigger(bodyB);
        }
        if (bodyB.isTrigger && bodyB.onTrigger) {
            bodyB.onTrigger(bodyA);
        }
    }

    /**
     * 解决碰撞
     */
    resolveCollision(bodyA, bodyB) {
        // 计算重叠
        const overlapX = Math.min(bodyA.bounds.right - bodyB.bounds.left, 
                                 bodyB.bounds.right - bodyA.bounds.left);
        const overlapY = Math.min(bodyA.bounds.bottom - bodyB.bounds.top,
                                 bodyB.bounds.bottom - bodyA.bounds.top);
        
        // 确定分离方向（重叠较小的轴）
        if (overlapX < overlapY) {
            // 水平分离
            this.resolveHorizontalCollision(bodyA, bodyB, overlapX);
        } else {
            // 垂直分离
            this.resolveVerticalCollision(bodyA, bodyB, overlapY);
        }
        
        // 调用碰撞回调
        if (bodyA.onCollision) {
            bodyA.onCollision(bodyB);
        }
        if (bodyB.onCollision) {
            bodyB.onCollision(bodyA);
        }
    }

    /**
     * 解决水平碰撞
     */
    resolveHorizontalCollision(bodyA, bodyB, overlap) {
        const centerAX = bodyA.x + bodyA.width / 2;
        const centerBX = bodyB.x + bodyB.width / 2;
        
        if (centerAX < centerBX) {
            // A在B左边
            if (!bodyA.isStatic) {
                bodyA.x -= overlap / 2;
                bodyA.velocityX = Math.min(0, bodyA.velocityX);
            }
            if (!bodyB.isStatic) {
                bodyB.x += overlap / 2;
                bodyB.velocityX = Math.max(0, bodyB.velocityX);
            }
        } else {
            // A在B右边
            if (!bodyA.isStatic) {
                bodyA.x += overlap / 2;
                bodyA.velocityX = Math.max(0, bodyA.velocityX);
            }
            if (!bodyB.isStatic) {
                bodyB.x -= overlap / 2;
                bodyB.velocityX = Math.min(0, bodyB.velocityX);
            }
        }
        
        // 更新边界
        this.updateBodyBounds(bodyA);
        this.updateBodyBounds(bodyB);
    }

    /**
     * 解决垂直碰撞
     */
    resolveVerticalCollision(bodyA, bodyB, overlap) {
        const centerAY = bodyA.y + bodyA.height / 2;
        const centerBY = bodyB.y + bodyB.height / 2;
        
        if (centerAY < centerBY) {
            // A在B上方
            if (!bodyA.isStatic) {
                bodyA.y -= overlap / 2;
                if (bodyA.velocityY > 0) {
                    bodyA.velocityY = 0;
                    bodyA.onGround = true;
                }
            }
            if (!bodyB.isStatic) {
                bodyB.y += overlap / 2;
                if (bodyB.velocityY < 0) {
                    bodyB.velocityY = 0;
                }
            }
        } else {
            // A在B下方
            if (!bodyA.isStatic) {
                bodyA.y += overlap / 2;
                if (bodyA.velocityY < 0) {
                    bodyA.velocityY = 0;
                }
            }
            if (!bodyB.isStatic) {
                bodyB.y -= overlap / 2;
                if (bodyB.velocityY > 0) {
                    bodyB.velocityY = 0;
                    bodyB.onGround = true;
                }
            }
        }
        
        // 更新边界
        this.updateBodyBounds(bodyA);
        this.updateBodyBounds(bodyB);
    }

    /**
     * 添加到空间网格
     */
    addToSpatialGrid(body) {
        const cells = this.getBodyCells(body);
        for (const cell of cells) {
            if (!this.spatialGrid.grid.has(cell)) {
                this.spatialGrid.grid.set(cell, new Set());
            }
            this.spatialGrid.grid.get(cell).add(body);
        }
    }

    /**
     * 从空间网格移除
     */
    removeFromSpatialGrid(body) {
        const cells = this.getBodyCells(body);
        for (const cell of cells) {
            const cellSet = this.spatialGrid.grid.get(cell);
            if (cellSet) {
                cellSet.delete(body);
                if (cellSet.size === 0) {
                    this.spatialGrid.grid.delete(cell);
                }
            }
        }
    }

    /**
     * 更新空间网格
     */
    updateSpatialGrid() {
        // 重建网格（简化实现）
        this.spatialGrid.grid.clear();
        
        for (const body of this.bodies.values()) {
            this.addToSpatialGrid(body);
        }
        
        for (const body of this.staticBodies.values()) {
            this.addToSpatialGrid(body);
        }
    }

    /**
     * 获取物体占用的网格单元
     */
    getBodyCells(body) {
        const cells = [];
        const cellSize = this.spatialGrid.cellSize;
        
        const startX = Math.floor(body.bounds.left / cellSize);
        const endX = Math.floor(body.bounds.right / cellSize);
        const startY = Math.floor(body.bounds.top / cellSize);
        const endY = Math.floor(body.bounds.bottom / cellSize);
        
        for (let x = startX; x <= endX; x++) {
            for (let y = startY; y <= endY; y++) {
                cells.push(`${x},${y}`);
            }
        }
        
        return cells;
    }

    /**
     * 获取附近的物体
     */
    getNearbyBodies(body, bodyMap) {
        const nearby = new Set();
        const cells = this.getBodyCells(body);
        
        for (const cell of cells) {
            const cellBodies = this.spatialGrid.grid.get(cell);
            if (cellBodies) {
                for (const cellBody of cellBodies) {
                    if (bodyMap.has(cellBody.id) && cellBody !== body) {
                        nearby.add(cellBody);
                    }
                }
            }
        }
        
        return nearby;
    }

    /**
     * 射线投射
     */
    raycast(startX, startY, endX, endY, layerMask = ['default']) {
        const results = [];
        const dx = endX - startX;
        const dy = endY - startY;
        const length = Math.sqrt(dx * dx + dy * dy);
        
        if (length === 0) return results;
        
        const dirX = dx / length;
        const dirY = dy / length;
        const steps = Math.ceil(length / 8); // 每8像素检查一次
        
        for (let i = 0; i <= steps; i++) {
            const x = startX + dirX * (i * 8);
            const y = startY + dirY * (i * 8);
            
            // 检查这个点是否与任何物体相交
            for (const body of this.staticBodies.values()) {
                if (!layerMask.includes(body.layer)) continue;
                
                if (x >= body.bounds.left && x <= body.bounds.right &&
                    y >= body.bounds.top && y <= body.bounds.bottom) {
                    
                    const distance = Math.sqrt((x - startX) ** 2 + (y - startY) ** 2);
                    results.push({
                        body,
                        point: { x, y },
                        distance,
                        normal: this.calculateNormal(body, x, y)
                    });
                    
                    return results; // 返回第一个碰撞
                }
            }
        }
        
        return results;
    }

    /**
     * 计算法线
     */
    calculateNormal(body, x, y) {
        const centerX = body.x + body.width / 2;
        const centerY = body.y + body.height / 2;
        
        const dx = x - centerX;
        const dy = y - centerY;
        
        // 简化：返回主要方向的法线
        if (Math.abs(dx) > Math.abs(dy)) {
            return { x: dx > 0 ? 1 : -1, y: 0 };
        } else {
            return { x: 0, y: dy > 0 ? 1 : -1 };
        }
    }

    /**
     * 应用力到物体
     */
    applyForce(bodyId, forceX, forceY) {
        const body = this.getBody(bodyId);
        if (body && !body.isStatic) {
            body.accelerationX += forceX / body.mass;
            body.accelerationY += forceY / body.mass;
        }
    }

    /**
     * 应用冲量到物体
     */
    applyImpulse(bodyId, impulseX, impulseY) {
        const body = this.getBody(bodyId);
        if (body && !body.isStatic) {
            body.velocityX += impulseX / body.mass;
            body.velocityY += impulseY / body.mass;
        }
    }

    /**
     * 设置物体位置
     */
    setBodyPosition(bodyId, x, y) {
        const body = this.getBody(bodyId);
        if (body) {
            body.x = x;
            body.y = y;
            this.updateBodyBounds(body);
        }
    }

    /**
     * 设置物体速度
     */
    setBodyVelocity(bodyId, velocityX, velocityY) {
        const body = this.getBody(bodyId);
        if (body && !body.isStatic) {
            body.velocityX = velocityX;
            body.velocityY = velocityY;
        }
    }

    /**
     * 获取统计信息
     */
    getStats() {
        return { ...this.stats };
    }

    /**
     * 设置重力
     */
    setGravity(gravity) {
        this.gravity = gravity;
    }

    /**
     * 获取重力
     */
    getGravity() {
        return this.gravity;
    }

    /**
     * 清理所有物理体
     */
    clear() {
        this.bodies.clear();
        this.staticBodies.clear();
        this.spatialGrid.grid.clear();
    }

    /**
     * 释放资源
     */
    dispose() {
        this.clear();
        console.log('物理系统已释放资源');
    }
}