/**
 * 着色器管理器
 * 负责创建、编译和管理WebGL着色器程序
 */
export class ShaderManager {
    constructor(gl) {
        this.gl = gl;
        this.programs = new Map();
        this.currentProgram = null;
        
        // 着色器源代码缓存
        this.vertexShaderSources = new Map();
        this.fragmentShaderSources = new Map();
        
        // 预定义着色器
        this.initializeShaderSources();
    }

    /**
     * 初始化预定义的着色器源代码
     */
    initializeShaderSources() {
        // 精灵渲染着色器 - 顶点着色器
        this.vertexShaderSources.set('sprite', `
            attribute vec2 a_position;
            attribute vec2 a_texCoord;
            attribute vec4 a_color;
            
            uniform mat4 u_projection;
            uniform mat4 u_view;
            uniform mat4 u_model;
            
            varying vec2 v_texCoord;
            varying vec4 v_color;
            
            void main() {
                gl_Position = u_projection * u_view * u_model * vec4(a_position, 0.0, 1.0);
                v_texCoord = a_texCoord;
                v_color = a_color;
            }
        `);

        // 精灵渲染着色器 - 片段着色器
        this.fragmentShaderSources.set('sprite', `
            precision mediump float;
            
            uniform sampler2D u_texture;
            uniform float u_alpha;
            
            varying vec2 v_texCoord;
            varying vec4 v_color;
            
            void main() {
                vec4 texColor = texture2D(u_texture, v_texCoord);
                gl_FragColor = texColor * v_color * u_alpha;
                
                // 像素艺术透明处理
                if (gl_FragColor.a < 0.1) {
                    discard;
                }
            }
        `);

        // 批量精灵渲染着色器 - 顶点着色器（支持实例化）
        this.vertexShaderSources.set('batchSprite', `
            attribute vec2 a_position;
            attribute vec2 a_texCoord;
            
            // 实例属性
            attribute vec2 a_instancePos;
            attribute vec2 a_instanceScale;
            attribute float a_instanceRotation;
            attribute vec4 a_instanceColor;
            attribute vec4 a_instanceTexRegion; // x,y,width,height
            
            uniform mat4 u_projection;
            uniform mat4 u_view;
            
            varying vec2 v_texCoord;
            varying vec4 v_color;
            
            void main() {
                // 旋转和缩放变换
                float cos_r = cos(a_instanceRotation);
                float sin_r = sin(a_instanceRotation);
                
                vec2 rotatedPos = vec2(
                    a_position.x * cos_r - a_position.y * sin_r,
                    a_position.x * sin_r + a_position.y * cos_r
                );
                
                vec2 scaledPos = rotatedPos * a_instanceScale;
                vec2 worldPos = scaledPos + a_instancePos;
                
                gl_Position = u_projection * u_view * vec4(worldPos, 0.0, 1.0);
                
                // 纹理坐标映射
                v_texCoord = a_instanceTexRegion.xy + a_position * a_instanceTexRegion.zw;
                v_color = a_instanceColor;
            }
        `);

        // 批量精灵着色器 - 片段着色器
        this.fragmentShaderSources.set('batchSprite', `
            precision mediump float;
            
            uniform sampler2D u_texture;
            
            varying vec2 v_texCoord;
            varying vec4 v_color;
            
            void main() {
                vec4 texColor = texture2D(u_texture, v_texCoord);
                gl_FragColor = texColor * v_color;
                
                if (gl_FragColor.a < 0.1) {
                    discard;
                }
            }
        `);

        // 背景渲染着色器 - 顶点着色器
        this.vertexShaderSources.set('background', `
            attribute vec2 a_position;
            attribute vec2 a_texCoord;
            
            uniform mat4 u_projection;
            uniform vec2 u_scroll; // 视差滚动偏移
            
            varying vec2 v_texCoord;
            
            void main() {
                gl_Position = u_projection * vec4(a_position, 0.0, 1.0);
                v_texCoord = a_texCoord + u_scroll;
            }
        `);

        // 背景着色器 - 片段着色器
        this.fragmentShaderSources.set('background', `
            precision mediump float;
            
            uniform sampler2D u_texture;
            uniform vec4 u_color;
            
            varying vec2 v_texCoord;
            
            void main() {
                vec4 texColor = texture2D(u_texture, v_texCoord);
                gl_FragColor = texColor * u_color;
            }
        `);

        // 调试线框着色器 - 顶点着色器
        this.vertexShaderSources.set('debug', `
            attribute vec2 a_position;
            
            uniform mat4 u_projection;
            uniform mat4 u_view;
            uniform mat4 u_model;
            
            void main() {
                gl_Position = u_projection * u_view * u_model * vec4(a_position, 0.0, 1.0);
            }
        `);

        // 调试线框着色器 - 片段着色器
        this.fragmentShaderSources.set('debug', `
            precision mediump float;
            
            uniform vec4 u_color;
            
            void main() {
                gl_FragColor = u_color;
            }
        `);
    }

    /**
     * 编译单个着色器
     */
    compileShader(source, type) {
        const gl = this.gl;
        const shader = gl.createShader(type);
        
        gl.shaderSource(shader, source);
        gl.compileShader(shader);
        
        if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
            const error = gl.getShaderInfoLog(shader);
            gl.deleteShader(shader);
            throw new Error(`着色器编译失败: ${error}`);
        }
        
        return shader;
    }

    /**
     * 创建着色器程序
     */
    createProgram(vertexShader, fragmentShader) {
        const gl = this.gl;
        const program = gl.createProgram();
        
        gl.attachShader(program, vertexShader);
        gl.attachShader(program, fragmentShader);
        gl.linkProgram(program);
        
        if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
            const error = gl.getProgramInfoLog(program);
            gl.deleteProgram(program);
            throw new Error(`着色器程序链接失败: ${error}`);
        }
        
        // 获取属性和uniform位置
        const programInfo = this.analyzeProgramInfo(program);
        
        return { program, info: programInfo };
    }

    /**
     * 分析着色器程序信息
     */
    analyzeProgramInfo(program) {
        const gl = this.gl;
        const info = {
            attributes: new Map(),
            uniforms: new Map()
        };
        
        // 获取属性信息
        const numAttributes = gl.getProgramParameter(program, gl.ACTIVE_ATTRIBUTES);
        for (let i = 0; i < numAttributes; i++) {
            const attribute = gl.getActiveAttrib(program, i);
            const location = gl.getAttribLocation(program, attribute.name);
            info.attributes.set(attribute.name, {
                location,
                type: attribute.type,
                size: attribute.size
            });
        }
        
        // 获取uniform信息
        const numUniforms = gl.getProgramParameter(program, gl.ACTIVE_UNIFORMS);
        for (let i = 0; i < numUniforms; i++) {
            const uniform = gl.getActiveUniform(program, i);
            const location = gl.getUniformLocation(program, uniform.name);
            info.uniforms.set(uniform.name, {
                location,
                type: uniform.type,
                size: uniform.size
            });
        }
        
        return info;
    }

    /**
     * 加载预定义着色器
     */
    async loadShaders() {
        const shaderTypes = ['sprite', 'batchSprite', 'background', 'debug'];
        
        for (const type of shaderTypes) {
            try {
                const vertexSource = this.vertexShaderSources.get(type);
                const fragmentSource = this.fragmentShaderSources.get(type);
                
                if (!vertexSource || !fragmentSource) {
                    throw new Error(`着色器源代码未找到: ${type}`);
                }
                
                const vertexShader = this.compileShader(vertexSource, this.gl.VERTEX_SHADER);
                const fragmentShader = this.compileShader(fragmentSource, this.gl.FRAGMENT_SHADER);
                
                const programData = this.createProgram(vertexShader, fragmentShader);
                this.programs.set(type, programData);
                
                console.log(`着色器程序 "${type}" 加载成功`);
                console.log(`  Uniforms:`, Array.from(programData.info.uniforms.keys()));
                console.log(`  Attributes:`, Array.from(programData.info.attributes.keys()));
                
                // 清理着色器对象
                this.gl.deleteShader(vertexShader);
                this.gl.deleteShader(fragmentShader);
                
            } catch (error) {
                console.error(`加载着色器 "${type}" 失败:`, error);
                throw error;
            }
        }
    }

    /**
     * 使用指定的着色器程序
     */
    useProgram(name) {
        const programData = this.programs.get(name);
        if (!programData) {
            throw new Error(`着色器程序未找到: ${name}`);
        }
        
        if (this.currentProgram !== programData.program) {
            this.gl.useProgram(programData.program);
            this.currentProgram = programData.program;
        }
        
        return programData;
    }

    /**
     * 获取当前使用的程序信息
     */
    getCurrentProgramInfo(name) {
        const programData = this.programs.get(name);
        return programData ? programData.info : null;
    }

    /**
     * 设置uniform值
     */
    setUniform(programName, uniformName, value) {
        const programData = this.programs.get(programName);
        if (!programData) {
            console.warn(`着色器程序未找到: ${programName}`);
            return;
        }
        
        const uniformInfo = programData.info.uniforms.get(uniformName);
        if (!uniformInfo) {
            console.warn(`Uniform未找到: ${uniformName} in ${programName}`);
            return;
        }
        
        const gl = this.gl;
        const location = uniformInfo.location;
        const type = uniformInfo.type;
        
        // 确保使用正确的程序
        this.useProgram(programName);
        
        // 根据类型设置uniform值
        switch (type) {
            case gl.FLOAT:
                gl.uniform1f(location, value);
                break;
            case gl.FLOAT_VEC2:
                gl.uniform2fv(location, value);
                break;
            case gl.FLOAT_VEC3:
                gl.uniform3fv(location, value);
                break;
            case gl.FLOAT_VEC4:
                gl.uniform4fv(location, value);
                break;
            case gl.FLOAT_MAT4:
                gl.uniformMatrix4fv(location, false, value);
                break;
            case gl.SAMPLER_2D:
                gl.uniform1i(location, value);
                break;
            case gl.INT:
                gl.uniform1i(location, value);
                break;
            case gl.BOOL:
                gl.uniform1i(location, value ? 1 : 0);
                break;
            default:
                console.warn(`不支持的uniform类型: ${type}`);
        }
    }

    /**
     * 批量设置uniform值
     */
    setUniforms(programName, uniforms) {
        for (const [name, value] of Object.entries(uniforms)) {
            this.setUniform(programName, name, value);
        }
    }

    /**
     * 启用顶点属性
     */
    enableAttribute(programName, attributeName, size, type, normalized, stride, offset) {
        const programData = this.programs.get(programName);
        if (!programData) {
            console.warn(`着色器程序未找到: ${programName}`);
            return;
        }
        
        const attributeInfo = programData.info.attributes.get(attributeName);
        if (!attributeInfo) {
            console.warn(`属性未找到: ${attributeName} in ${programName}`);
            return;
        }
        
        const gl = this.gl;
        const location = attributeInfo.location;
        
        gl.enableVertexAttribArray(location);
        gl.vertexAttribPointer(location, size, type, normalized, stride, offset);
        
        return location;
    }

    /**
     * 禁用顶点属性
     */
    disableAttribute(programName, attributeName) {
        const programData = this.programs.get(programName);
        if (!programData) {
            return;
        }
        
        const attributeInfo = programData.info.attributes.get(attributeName);
        if (!attributeInfo) {
            return;
        }
        
        this.gl.disableVertexAttribArray(attributeInfo.location);
    }

    /**
     * 获取属性位置
     */
    getAttributeLocation(programName, attributeName) {
        const programData = this.programs.get(programName);
        if (!programData) {
            return -1;
        }
        
        const attributeInfo = programData.info.attributes.get(attributeName);
        return attributeInfo ? attributeInfo.location : -1;
    }

    /**
     * 获取uniform位置
     */
    getUniformLocation(programName, uniformName) {
        const programData = this.programs.get(programName);
        if (!programData) {
            return null;
        }
        
        const uniformInfo = programData.info.uniforms.get(uniformName);
        return uniformInfo ? uniformInfo.location : null;
    }

    /**
     * 添加自定义着色器
     */
    addCustomShader(name, vertexSource, fragmentSource) {
        try {
            const vertexShader = this.compileShader(vertexSource, this.gl.VERTEX_SHADER);
            const fragmentShader = this.compileShader(fragmentSource, this.gl.FRAGMENT_SHADER);
            
            const programData = this.createProgram(vertexShader, fragmentShader);
            this.programs.set(name, programData);
            
            // 清理着色器对象
            this.gl.deleteShader(vertexShader);
            this.gl.deleteShader(fragmentShader);
            
            console.log(`自定义着色器程序 "${name}" 添加成功`);
            
        } catch (error) {
            console.error(`添加自定义着色器 "${name}" 失败:`, error);
            throw error;
        }
    }

    /**
     * 获取所有可用的着色器程序名称
     */
    getProgramNames() {
        return Array.from(this.programs.keys());
    }

    /**
     * 检查着色器程序是否存在
     */
    hasProgram(name) {
        return this.programs.has(name);
    }

    /**
     * 释放所有着色器资源
     */
    dispose() {
        const gl = this.gl;
        
        for (const [name, programData] of this.programs) {
            gl.deleteProgram(programData.program);
        }
        
        this.programs.clear();
        this.currentProgram = null;
        this.vertexShaderSources.clear();
        this.fragmentShaderSources.clear();
        
        console.log('着色器管理器资源已释放');
    }

    /**
     * 重新加载所有着色器（用于热重载）
     */
    async reload() {
        this.dispose();
        this.initializeShaderSources();
        await this.loadShaders();
        console.log('着色器已重新加载');
    }
}