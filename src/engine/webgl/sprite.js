/**
 * 精灵渲染器
 * 负责高效渲染2D精灵和批量渲染优化
 */
export class SpriteRenderer {
    constructor(gl, shaderManager, textureManager) {
        this.gl = gl;
        this.shaderManager = shaderManager;
        this.textureManager = textureManager;
        
        // 渲染批次
        this.batches = [];
        this.currentBatch = null;
        this.maxBatchSize = 1000; // 每批次最大精灵数
        
        // 顶点数据
        this.vertexBuffer = null;
        this.indexBuffer = null;
        this.instanceBuffer = null;
        
        // 几何数据
        this.quadVertices = new Float32Array([
            // 位置 (x, y), 纹理坐标 (u, v)
            0, 0, 0, 0,  // 左下
            1, 0, 1, 0,  // 右下
            1, 1, 1, 1,  // 右上
            0, 1, 0, 1   // 左上
        ]);
        
        this.quadIndices = new Uint16Array([
            0, 1, 2,  // 第一个三角形
            0, 2, 3   // 第二个三角形
        ]);
        
        // 实例数据缓冲区
        this.instanceData = new Float32Array(this.maxBatchSize * 11); // 每实例11个float
        this.instanceCount = 0;
        
        // 变换矩阵缓存
        this.modelMatrix = new Float32Array(16);
        
        // 初始化
        this.initialize();
    }

    /**
     * 初始化渲染器
     */
    initialize() {
        this.createBuffers();
        console.log('精灵渲染器初始化完成');
    }

    /**
     * 创建顶点和索引缓冲区
     */
    createBuffers() {
        const gl = this.gl;
        
        // 创建四边形顶点缓冲区
        this.vertexBuffer = gl.createBuffer();
        gl.bindBuffer(gl.ARRAY_BUFFER, this.vertexBuffer);
        gl.bufferData(gl.ARRAY_BUFFER, this.quadVertices, gl.STATIC_DRAW);
        
        // 创建索引缓冲区
        this.indexBuffer = gl.createBuffer();
        gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, this.indexBuffer);
        gl.bufferData(gl.ELEMENT_ARRAY_BUFFER, this.quadIndices, gl.STATIC_DRAW);
        
        // 创建实例数据缓冲区
        this.instanceBuffer = gl.createBuffer();
        gl.bindBuffer(gl.ARRAY_BUFFER, this.instanceBuffer);
        gl.bufferData(gl.ARRAY_BUFFER, this.instanceData, gl.DYNAMIC_DRAW);
    }

    /**
     * 开始渲染批次
     */
    begin() {
        this.batches = [];
        this.currentBatch = null;
        this.instanceCount = 0;
    }

    /**
     * 渲染单个精灵
     */
    drawSprite(config) {
        const {
            texture,
            x = 0, y = 0,
            width = 32, height = 32,
            originX = 0, originY = 0,
            scaleX = 1, scaleY = 1,
            rotation = 0,
            color = [1, 1, 1, 1],
            texRegion = null,
            flipX = false, flipY = false
        } = config;

        // 直接使用简单渲染方式
        this.drawSpriteSimple(texture, x, y, width, height, {
            rotation,
            scaleX,
            scaleY,
            originX,
            originY,
            color,
            flipX,
            texRegion
        });
    }

    /**
     * 开始新的渲染批次
     */
    startNewBatch(texture) {
        this.currentBatch = {
            texture: texture,
            count: 0,
            startIndex: this.instanceCount
        };
        this.batches.push(this.currentBatch);
    }

    /**
     * 刷新当前批次
     */
    flushCurrentBatch() {
        if (this.currentBatch && this.currentBatch.count > 0) {
            // 当前批次会在end()中一起处理
        }
    }

    /**
     * 使用简单着色器渲染精灵（向后兼容）
     */
    drawSpriteSimple(texture, x, y, width, height, options = {}) {
        const gl = this.gl;
        
        // 只在第一次调用时输出调试信息
        if (!this.debugLogged) {
            console.log(`drawSpriteSimple首次调用: 纹理=${texture}, 位置=(${x}, ${y}), 大小=(${width}x${height})`);
            this.debugLogged = true;
        }
        
        try {
            // 使用sprite着色器
            const programData = this.shaderManager.useProgram('sprite');
            
            // 绑定纹理
            this.textureManager.bindTexture(texture, 0);
            
            // 设置模型矩阵
            this.createModelMatrix(
                x, y, 
                width, height,
                options.rotation || 0,
                options.scaleX || 1,
                options.scaleY || 1,
                options.originX || 0,
                options.originY || 0
            );
            
            // 设置uniforms
            try {
                this.shaderManager.setUniform('sprite', 'u_model', this.modelMatrix);
                this.shaderManager.setUniform('sprite', 'u_texture', 0);
                this.shaderManager.setUniform('sprite', 'u_alpha', options.alpha || 1.0);
            } catch (e) {
                // 忽略uniform不存在的错误
            }
            
            // 绑定顶点数据
            gl.bindBuffer(gl.ARRAY_BUFFER, this.vertexBuffer);
            
            // 设置顶点属性
            const posLoc = this.shaderManager.getAttributeLocation('sprite', 'a_position');
            const texLoc = this.shaderManager.getAttributeLocation('sprite', 'a_texCoord');
            
            if (posLoc >= 0) {
                gl.enableVertexAttribArray(posLoc);
                gl.vertexAttribPointer(posLoc, 2, gl.FLOAT, false, 16, 0);
            }
            
            if (texLoc >= 0) {
                gl.enableVertexAttribArray(texLoc);
                gl.vertexAttribPointer(texLoc, 2, gl.FLOAT, false, 16, 8);
            }
            
            // 设置颜色（如果支持）
            const colorLoc = this.shaderManager.getAttributeLocation('sprite', 'a_color');
            if (colorLoc >= 0 && options.color) {
                gl.vertexAttrib4fv(colorLoc, options.color);
            }
            
            // 绑定索引并绘制
            gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, this.indexBuffer);
            gl.drawElements(gl.TRIANGLES, 6, gl.UNSIGNED_SHORT, 0);
            
            // 清理
            if (posLoc >= 0) gl.disableVertexAttribArray(posLoc);
            if (texLoc >= 0) gl.disableVertexAttribArray(texLoc);
            
        } catch (error) {
            console.warn('精灵渲染失败:', error);
        }
    }

    /**
     * 后备渲染方法（当着色器失败时使用）
     */
    renderFallbackSprite(x, y, width, height) {
        // 这里可以实现一个简单的颜色矩形渲染作为后备
        console.log(`后备渲染: ${x}, ${y}, ${width}x${height}`);
    }

    /**
     * 结束渲染并刷新所有批次
     */
    end() {
        // 对于简化版本，我们直接使用简单的sprite着色器渲染每个精灵
        // 不使用批量实例化渲染来保持WebGL 1.0兼容性
        this.cleanup();
    }


    /**
     * 创建模型变换矩阵
     */
    createModelMatrix(x, y, width, height, rotation, scaleX, scaleY, originX, originY) {
        const matrix = this.modelMatrix;
        
        // 重置为单位矩阵
        matrix.fill(0);
        matrix[0] = matrix[5] = matrix[10] = matrix[15] = 1;
        
        // 平移到目标位置
        matrix[12] = x;
        matrix[13] = y;
        
        // 如果有旋转或缩放，需要更复杂的变换
        if (rotation !== 0 || scaleX !== 1 || scaleY !== 1 || originX !== 0 || originY !== 0) {
            this.applyTransform(matrix, x, y, width, height, rotation, scaleX, scaleY, originX, originY);
        } else {
            // 简单缩放
            matrix[0] = width;
            matrix[5] = height;
        }
    }

    /**
     * 应用复杂变换
     */
    applyTransform(matrix, x, y, width, height, rotation, scaleX, scaleY, originX, originY) {
        // 计算原点偏移
        const offsetX = width * originX;
        const offsetY = height * originY;
        
        // 平移到原点
        matrix[12] = x + offsetX;
        matrix[13] = y + offsetY;
        
        if (rotation !== 0) {
            const cos = Math.cos(rotation);
            const sin = Math.sin(rotation);
            
            // 旋转矩阵
            const m00 = cos * width * scaleX;
            const m01 = -sin * height * scaleY;
            const m10 = sin * width * scaleX;
            const m11 = cos * height * scaleY;
            
            matrix[0] = m00;
            matrix[1] = m10;
            matrix[4] = m01;
            matrix[5] = m11;
            
            // 调整平移以考虑旋转后的原点偏移
            matrix[12] = x - offsetX * cos + offsetY * sin;
            matrix[13] = y - offsetX * sin - offsetY * cos;
        } else {
            matrix[0] = width * scaleX;
            matrix[5] = height * scaleY;
            matrix[12] = x - offsetX;
            matrix[13] = y - offsetY;
        }
    }

    /**
     * 清理渲染状态
     */
    cleanup() {
        // 禁用所有顶点属性数组
        const gl = this.gl;
        const maxAttribs = gl.getParameter(gl.MAX_VERTEX_ATTRIBS);
        for (let i = 0; i < maxAttribs; i++) {
            gl.disableVertexAttribArray(i);
            // 不使用 vertexAttribDivisor 以保持 WebGL 1.0 兼容性
        }
    }

    /**
     * 绘制调试矩形
     */
    drawDebugRect(x, y, width, height, color = [1, 0, 0, 1]) {
        const gl = this.gl;
        
        // 使用调试着色器
        const programData = this.shaderManager.useProgram('debug');
        
        // 创建变换矩阵
        this.createModelMatrix(x, y, width, height, 0, 1, 1, 0, 0);
        
        // 设置uniforms
        this.shaderManager.setUniform('debug', 'u_model', this.modelMatrix);
        this.shaderManager.setUniform('debug', 'u_color', color);
        
        // 绑定顶点数据
        gl.bindBuffer(gl.ARRAY_BUFFER, this.vertexBuffer);
        
        const posLoc = programData.info.attributes.get('a_position').location;
        gl.enableVertexAttribArray(posLoc);
        gl.vertexAttribPointer(posLoc, 2, gl.FLOAT, false, 16, 0);
        
        // 使用线框模式绘制
        gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, this.indexBuffer);
        
        // 绘制线框
        const prevLineWidth = gl.getParameter(gl.LINE_WIDTH);
        gl.lineWidth(2);
        
        // 手动绘制矩形边框
        const lineIndices = new Uint16Array([0, 1, 1, 2, 2, 3, 3, 0]);
        const lineBuffer = gl.createBuffer();
        gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, lineBuffer);
        gl.bufferData(gl.ELEMENT_ARRAY_BUFFER, lineIndices, gl.STATIC_DRAW);
        
        gl.drawElements(gl.LINES, 8, gl.UNSIGNED_SHORT, 0);
        
        gl.lineWidth(prevLineWidth);
        gl.deleteBuffer(lineBuffer);
        
        gl.disableVertexAttribArray(posLoc);
    }

    /**
     * 获取渲染统计
     */
    getStats() {
        return {
            batches: this.batches.length,
            instances: this.instanceCount,
            maxBatchSize: this.maxBatchSize
        };
    }

    /**
     * 重置统计
     */
    resetStats() {
        this.batches = [];
        this.instanceCount = 0;
    }

    /**
     * 释放资源
     */
    dispose() {
        const gl = this.gl;
        
        if (this.vertexBuffer) {
            gl.deleteBuffer(this.vertexBuffer);
        }
        if (this.indexBuffer) {
            gl.deleteBuffer(this.indexBuffer);
        }
        if (this.instanceBuffer) {
            gl.deleteBuffer(this.instanceBuffer);
        }
        
        this.vertexBuffer = null;
        this.indexBuffer = null;
        this.instanceBuffer = null;
        
        console.log('精灵渲染器资源已释放');
    }
}