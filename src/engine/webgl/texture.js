/**
 * 纹理管理器
 * 负责加载、缓存和管理WebGL纹理资源
 */
export class TextureManager {
    constructor(gl) {
        this.gl = gl;
        this.textures = new Map();
        this.textureAtlas = new Map();
        this.currentTextureUnit = 0;
        this.maxTextureUnits = gl.getParameter(gl.MAX_TEXTURE_IMAGE_UNITS);
        
        // 默认纹理
        this.defaultTexture = null;
        this.whiteTexture = null;
        
        // 加载状态
        this.loadPromises = new Map();
        
        // 创建默认纹理
        this.createDefaultTextures();
    }

    /**
     * 创建默认纹理
     */
    createDefaultTextures() {
        // 创建1x1白色纹理作为默认纹理
        this.whiteTexture = this.createSolidColorTexture([255, 255, 255, 255]);
        this.textures.set('__white', this.whiteTexture);
        
        // 创建1x1粉红色纹理作为错误纹理
        this.defaultTexture = this.createSolidColorTexture([255, 0, 255, 255]);
        this.textures.set('__default', this.defaultTexture);
        
        // 创建透明纹理
        const transparentTexture = this.createSolidColorTexture([255, 255, 255, 0]);
        this.textures.set('__transparent', transparentTexture);
    }

    /**
     * 创建纯色纹理
     */
    createSolidColorTexture(color) {
        const gl = this.gl;
        const texture = gl.createTexture();
        
        gl.bindTexture(gl.TEXTURE_2D, texture);
        
        // 创建1x1像素的纹理
        gl.texImage2D(
            gl.TEXTURE_2D,
            0,              // mip level
            gl.RGBA,        // internal format
            1,              // width
            1,              // height
            0,              // border
            gl.RGBA,        // format
            gl.UNSIGNED_BYTE, // type
            new Uint8Array(color)
        );
        
        // 设置纹理参数（像素艺术样式）
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.NEAREST);
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.NEAREST);
        
        return {
            texture,
            width: 1,
            height: 1,
            loaded: true
        };
    }

    /**
     * 加载纹理图片
     */
    async loadTexture(name, url, options = {}) {
        // 如果已经存在加载Promise，返回它
        if (this.loadPromises.has(name)) {
            return this.loadPromises.get(name);
        }
        
        const loadPromise = this.loadTextureInternal(name, url, options);
        this.loadPromises.set(name, loadPromise);
        
        try {
            const result = await loadPromise;
            this.loadPromises.delete(name);
            return result;
        } catch (error) {
            this.loadPromises.delete(name);
            throw error;
        }
    }

    /**
     * 内部纹理加载实现
     */
    async loadTextureInternal(name, url, options) {
        return new Promise((resolve, reject) => {
            const image = new Image();
            
            image.onload = () => {
                try {
                    const textureData = this.createTextureFromImage(image, options);
                    this.textures.set(name, textureData);
                    
                    console.log(`纹理 "${name}" 加载成功 (${image.width}x${image.height})`);
                    resolve(textureData);
                } catch (error) {
                    console.error(`创建纹理 "${name}" 失败:`, error);
                    reject(error);
                }
            };
            
            image.onerror = () => {
                console.error(`加载纹理图片失败: ${url}`);
                reject(new Error(`Failed to load texture image: ${url}`));
            };
            
            // 设置跨域支持
            if (options.crossOrigin !== false) {
                image.crossOrigin = options.crossOrigin || 'anonymous';
            }
            
            image.src = url;
        });
    }

    /**
     * 从Image对象创建纹理
     */
    createTextureFromImage(image, options = {}) {
        const gl = this.gl;
        const texture = gl.createTexture();
        
        gl.bindTexture(gl.TEXTURE_2D, texture);
        
        // 上传图片数据
        gl.texImage2D(
            gl.TEXTURE_2D,
            0,
            gl.RGBA,
            gl.RGBA,
            gl.UNSIGNED_BYTE,
            image
        );
        
        // 设置纹理参数
        const wrapS = options.wrapS || gl.CLAMP_TO_EDGE;
        const wrapT = options.wrapT || gl.CLAMP_TO_EDGE;
        const minFilter = options.minFilter || gl.NEAREST; // 像素艺术使用NEAREST
        const magFilter = options.magFilter || gl.NEAREST;
        
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, wrapS);
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, wrapT);
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, minFilter);
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, magFilter);
        
        // 如果需要生成mipmap且尺寸是2的幂
        if (options.generateMipmap && this.isPowerOf2(image.width) && this.isPowerOf2(image.height)) {
            gl.generateMipmap(gl.TEXTURE_2D);
        }
        
        return {
            texture,
            width: image.width,
            height: image.height,
            loaded: true,
            image: options.keepImage ? image : null
        };
    }

    /**
     * 检查数值是否为2的幂
     */
    isPowerOf2(value) {
        return (value & (value - 1)) === 0;
    }

    /**
     * 批量加载纹理
     */
    async loadTextures() {
        // 预定义的纹理资源列表
        const textureList = [
            { name: 'mario-small', url: this.generateMarioSprite('small') },
            { name: 'mario-big', url: this.generateMarioSprite('big') },
            { name: 'mario-fire', url: this.generateMarioSprite('fire') },
            { name: 'goomba', url: this.generateEnemySprite('goomba') },
            { name: 'koopa', url: this.generateEnemySprite('koopa') },
            { name: 'coin', url: this.generateItemSprite('coin') },
            { name: 'mushroom', url: this.generateItemSprite('mushroom') },
            { name: 'fireflower', url: this.generateItemSprite('fireflower') },
            { name: 'brick', url: this.generateTileSprite('brick') },
            { name: 'ground', url: this.generateTileSprite('ground') },
            { name: 'pipe', url: this.generateTileSprite('pipe') },
            { name: 'cloud', url: this.generateTileSprite('cloud') },
            { name: 'bush', url: this.generateTileSprite('bush') }
        ];
        
        const loadPromises = textureList.map(({ name, url }) => 
            this.loadTexture(name, url).catch(error => {
                console.warn(`纹理 ${name} 加载失败，使用默认纹理:`, error);
                return this.defaultTexture;
            })
        );
        
        await Promise.all(loadPromises);
        
        console.log(`成功加载 ${textureList.length} 个纹理`);
    }

    /**
     * 生成马里奥精灵图像（程序化生成）
     */
    generateMarioSprite(type) {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = 16;
        canvas.height = type === 'small' ? 16 : 32;
        
        // 清除背景
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // 设置像素化渲染
        ctx.imageSmoothingEnabled = false;
        
        // 马里奥颜色
        const colors = {
            red: '#FF0000',
            blue: '#0000FF',
            yellow: '#FFFF00',
            brown: '#8B4513',
            black: '#000000',
            white: '#FFFFFF',
            green: type === 'fire' ? '#00FF00' : '#8B4513'
        };
        
        // 绘制简单的马里奥像素图
        this.drawPixelArt(ctx, this.getMarioPixelPattern(type, colors));
        
        return canvas.toDataURL('image/png');
    }

    /**
     * 获取马里奥像素图案
     */
    getMarioPixelPattern(type, colors) {
        if (type === 'small') {
            return [
                '    RRRR    ',
                '  RRRRRRRR  ',
                '  BBBRBRBB  ',
                ' BRBBBBRBRB ',
                ' BRBBBBBRBB ',
                ' BBBBBBBBB  ',
                '   RRRRR    ',
                '  RRRRRR    ',
                ' RRR  RRR   ',
                'RRRRRRRRR   ',
                'RRRRRRRRR   ',
                'RRR    RRR  ',
                'BBB    BBB  ',
                'BBB    BBB  ',
                'BBBB  BBBB  ',
                '            '
            ];
        }
        
        // 大马里奥的图案会更复杂
        return [
            '    RRRR    ',
            '  RRRRRRRR  ',
            '  BBBRBRBB  ',
            ' BRBBBBRBRB ',
            ' BRBBBBBRBB ',
            ' BBBBBBBBB  ',
            '   RRRRR    '
        ];
    }

    /**
     * 绘制像素艺术
     */
    drawPixelArt(ctx, pattern) {
        const pixelSize = 1;
        const colorMap = {
            'R': '#FF0000', // 红色
            'B': '#8B4513', // 棕色
            'Y': '#FFFF00', // 黄色
            'G': '#00FF00', // 绿色
            'K': '#000000', // 黑色
            'W': '#FFFFFF', // 白色
            ' ': null       // 透明
        };
        
        for (let y = 0; y < pattern.length; y++) {
            for (let x = 0; x < pattern[y].length; x++) {
                const char = pattern[y][x];
                const color = colorMap[char];
                
                if (color) {
                    ctx.fillStyle = color;
                    ctx.fillRect(x * pixelSize, y * pixelSize, pixelSize, pixelSize);
                }
            }
        }
    }

    /**
     * 生成敌人精灵
     */
    generateEnemySprite(type) {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = 16;
        canvas.height = 16;
        
        ctx.imageSmoothingEnabled = false;
        
        if (type === 'goomba') {
            ctx.fillStyle = '#8B4513';
            ctx.fillRect(0, 8, 16, 8);
            ctx.fillStyle = '#000000';
            ctx.fillRect(4, 4, 2, 2);
            ctx.fillRect(10, 4, 2, 2);
        } else if (type === 'koopa') {
            ctx.fillStyle = '#00FF00';
            ctx.fillRect(2, 2, 12, 12);
            ctx.fillStyle = '#FFFF00';
            ctx.fillRect(4, 4, 8, 8);
        }
        
        return canvas.toDataURL('image/png');
    }

    /**
     * 生成道具精灵
     */
    generateItemSprite(type) {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = 16;
        canvas.height = 16;
        
        ctx.imageSmoothingEnabled = false;
        
        if (type === 'coin') {
            ctx.fillStyle = '#FFFF00';
            ctx.beginPath();
            ctx.arc(8, 8, 6, 0, Math.PI * 2);
            ctx.fill();
            ctx.fillStyle = '#FFA500';
            ctx.beginPath();
            ctx.arc(8, 8, 4, 0, Math.PI * 2);
            ctx.fill();
        } else if (type === 'mushroom') {
            ctx.fillStyle = '#FF0000';
            ctx.fillRect(2, 4, 12, 8);
            ctx.fillStyle = '#FFFFFF';
            ctx.fillRect(4, 6, 2, 2);
            ctx.fillRect(10, 6, 2, 2);
            ctx.fillStyle = '#FFFF00';
            ctx.fillRect(6, 12, 4, 4);
        }
        
        return canvas.toDataURL('image/png');
    }

    /**
     * 生成瓦片精灵
     */
    generateTileSprite(type) {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = 16;
        canvas.height = 16;
        
        ctx.imageSmoothingEnabled = false;
        
        if (type === 'brick') {
            ctx.fillStyle = '#8B4513';
            ctx.fillRect(0, 0, 16, 16);
            ctx.fillStyle = '#654321';
            for (let y = 0; y < 16; y += 4) {
                ctx.fillRect(0, y, 16, 1);
            }
        } else if (type === 'ground') {
            ctx.fillStyle = '#228B22';
            ctx.fillRect(0, 0, 16, 16);
            ctx.fillStyle = '#006400';
            for (let i = 0; i < 16; i += 2) {
                ctx.fillRect(i, 0, 1, 16);
            }
        }
        
        return canvas.toDataURL('image/png');
    }

    /**
     * 获取纹理
     */
    getTexture(name) {
        const textureData = this.textures.get(name);
        return textureData || this.defaultTexture;
    }

    /**
     * 绑定纹理到指定单元
     */
    bindTexture(name, unit = 0) {
        const textureData = this.getTexture(name);
        const gl = this.gl;
        
        // 调试：首次绑定时输出
        if (!this.bindDebugSet) {
            this.bindDebugSet = new Set();
        }
        if (!this.bindDebugSet.has(name)) {
            console.log(`首次绑定纹理 "${name}":`, textureData ? '成功' : '失败');
            this.bindDebugSet.add(name);
        }
        
        if (unit >= this.maxTextureUnits) {
            console.warn(`纹理单元 ${unit} 超出最大限制 ${this.maxTextureUnits}`);
            unit = 0;
        }
        
        gl.activeTexture(gl.TEXTURE0 + unit);
        gl.bindTexture(gl.TEXTURE_2D, textureData.texture);
        
        return unit;
    }

    /**
     * 创建纹理图集
     */
    createTextureAtlas(name, textures, layout) {
        // 计算图集大小
        const atlasSize = this.calculateAtlasSize(textures);
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        canvas.width = atlasSize.width;
        canvas.height = atlasSize.height;
        ctx.imageSmoothingEnabled = false;
        
        const regions = new Map();
        let currentX = 0;
        let currentY = 0;
        let rowHeight = 0;
        
        // 将纹理绘制到图集中
        for (const [texName, textureData] of textures) {
            if (!textureData.image) {
                console.warn(`纹理 ${texName} 没有保存图片数据，无法添加到图集`);
                continue;
            }
            
            const img = textureData.image;
            
            // 检查是否需要换行
            if (currentX + img.width > canvas.width) {
                currentX = 0;
                currentY += rowHeight;
                rowHeight = 0;
            }
            
            // 绘制图片
            ctx.drawImage(img, currentX, currentY);
            
            // 保存区域信息（归一化坐标）
            regions.set(texName, {
                x: currentX / canvas.width,
                y: currentY / canvas.height,
                width: img.width / canvas.width,
                height: img.height / canvas.height,
                pixelX: currentX,
                pixelY: currentY,
                pixelWidth: img.width,
                pixelHeight: img.height
            });
            
            currentX += img.width;
            rowHeight = Math.max(rowHeight, img.height);
        }
        
        // 创建图集纹理
        const atlasTexture = this.createTextureFromImage(canvas, {
            minFilter: this.gl.NEAREST,
            magFilter: this.gl.NEAREST
        });
        
        this.textures.set(name, atlasTexture);
        this.textureAtlas.set(name, regions);
        
        console.log(`纹理图集 "${name}" 创建成功 (${canvas.width}x${canvas.height})`);
        
        return { texture: atlasTexture, regions };
    }

    /**
     * 计算图集大小
     */
    calculateAtlasSize(textures) {
        let totalArea = 0;
        let maxWidth = 0;
        
        for (const [, textureData] of textures) {
            if (textureData.image) {
                totalArea += textureData.width * textureData.height;
                maxWidth = Math.max(maxWidth, textureData.width);
            }
        }
        
        // 估算正方形图集的边长
        let size = Math.ceil(Math.sqrt(totalArea));
        
        // 确保大小是2的幂
        size = this.nextPowerOf2(Math.max(size, maxWidth));
        
        return { width: size, height: size };
    }

    /**
     * 获取下一个2的幂
     */
    nextPowerOf2(value) {
        return Math.pow(2, Math.ceil(Math.log2(value)));
    }

    /**
     * 获取图集中的纹理区域
     */
    getAtlasRegion(atlasName, textureName) {
        const regions = this.textureAtlas.get(atlasName);
        return regions ? regions.get(textureName) : null;
    }

    /**
     * 检查纹理是否已加载
     */
    isTextureLoaded(name) {
        const textureData = this.textures.get(name);
        return textureData && textureData.loaded;
    }

    /**
     * 获取纹理尺寸
     */
    getTextureSize(name) {
        const textureData = this.textures.get(name);
        if (textureData) {
            return { width: textureData.width, height: textureData.height };
        }
        return { width: 1, height: 1 };
    }

    /**
     * 删除纹理
     */
    deleteTexture(name) {
        const textureData = this.textures.get(name);
        if (textureData) {
            this.gl.deleteTexture(textureData.texture);
            this.textures.delete(name);
            this.textureAtlas.delete(name);
        }
    }

    /**
     * 获取所有已加载的纹理名称
     */
    getTextureNames() {
        return Array.from(this.textures.keys());
    }

    /**
     * 获取纹理数量
     */
    getTextureCount() {
        return this.textures.size;
    }

    /**
     * 释放所有纹理资源
     */
    dispose() {
        for (const [name, textureData] of this.textures) {
            this.gl.deleteTexture(textureData.texture);
        }
        
        this.textures.clear();
        this.textureAtlas.clear();
        this.loadPromises.clear();
        
        this.defaultTexture = null;
        this.whiteTexture = null;
        
        console.log('纹理管理器资源已释放');
    }
}