/**
 * WebGL渲染器核心类
 * 负责WebGL上下文初始化、渲染状态管理和基础绘制功能
 */
export class WebGLRenderer {
    constructor(canvas) {
        this.canvas = canvas;
        this.gl = null;
        this.viewMatrix = new Float32Array(16);
        this.projectionMatrix = new Float32Array(16);
        this.camera = {
            x: 0,
            y: 0,
            zoom: 1,
            width: canvas.width,
            height: canvas.height
        };
        
        // 渲染状态
        this.clearColor = [0.3, 0.7, 1.0, 1.0]; // 天空蓝色
        this.enabledExtensions = {};
        
        // 性能统计
        this.stats = {
            drawCalls: 0,
            triangles: 0,
            vertices: 0
        };
    }

    /**
     * 初始化WebGL上下文和基础设置
     */
    initialize() {
        // 获取WebGL上下文
        this.gl = this.canvas.getContext('webgl', {
            antialias: false,
            alpha: false,
            depth: true,
            stencil: false,
            preserveDrawingBuffer: false,
            powerPreference: 'high-performance'
        });

        if (!this.gl) {
            // 尝试实验性WebGL
            this.gl = this.canvas.getContext('experimental-webgl');
        }

        if (!this.gl) {
            console.error('WebGL不被支持');
            return false;
        }

        // 启用必要的扩展
        this.enableExtensions();

        // 设置基础WebGL状态
        this.setupWebGLState();

        // 初始化投影矩阵
        this.updateProjectionMatrix();

        // 初始化视图矩阵
        this.updateViewMatrix();

        console.log('WebGL渲染器初始化成功');
        console.log('WebGL版本:', this.gl.getParameter(this.gl.VERSION));
        console.log('着色器版本:', this.gl.getParameter(this.gl.SHADING_LANGUAGE_VERSION));
        console.log('渲染器:', this.gl.getParameter(this.gl.RENDERER));

        return true;
    }

    /**
     * 启用WebGL扩展
     */
    enableExtensions() {
        const extensions = [
            'OES_vertex_array_object',
            'WEBGL_debug_renderer_info',
            'OES_element_index_uint'
        ];

        extensions.forEach(ext => {
            const extension = this.gl.getExtension(ext);
            if (extension) {
                this.enabledExtensions[ext] = extension;
                console.log(`启用扩展: ${ext}`);
            } else {
                console.warn(`扩展不可用: ${ext}`);
            }
        });
    }

    /**
     * 设置WebGL基础状态
     */
    setupWebGLState() {
        const gl = this.gl;

        // 启用混合
        gl.enable(gl.BLEND);
        gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);

        // 启用深度测试
        gl.enable(gl.DEPTH_TEST);
        gl.depthFunc(gl.LEQUAL);

        // 设置清除颜色
        gl.clearColor(...this.clearColor);

        // 设置视口
        gl.viewport(0, 0, this.canvas.width, this.canvas.height);

        // 启用剔除
        gl.enable(gl.CULL_FACE);
        gl.cullFace(gl.BACK);

        // 像素化设置（关键用于像素艺术）
        gl.pixelStorei(gl.UNPACK_PREMULTIPLY_ALPHA_WEBGL, false);
        gl.pixelStorei(gl.UNPACK_FLIP_Y_WEBGL, true);
    }

    /**
     * 更新投影矩阵
     */
    updateProjectionMatrix() {
        const width = this.camera.width;
        const height = this.camera.height;
        
        // 正交投影矩阵，适合2D游戏
        this.createOrthographicMatrix(
            this.projectionMatrix,
            0, width,    // left, right
            height, 0,   // bottom, top (翻转Y轴)
            -1000, 1000  // near, far
        );
    }

    /**
     * 更新视图矩阵
     */
    updateViewMatrix() {
        this.createViewMatrix(
            this.viewMatrix,
            this.camera.x,
            this.camera.y,
            this.camera.zoom
        );
    }

    /**
     * 创建正交投影矩阵
     */
    createOrthographicMatrix(out, left, right, bottom, top, near, far) {
        const lr = 1 / (left - right);
        const bt = 1 / (bottom - top);
        const nf = 1 / (near - far);
        
        out[0] = -2 * lr;
        out[1] = 0;
        out[2] = 0;
        out[3] = 0;
        out[4] = 0;
        out[5] = -2 * bt;
        out[6] = 0;
        out[7] = 0;
        out[8] = 0;
        out[9] = 0;
        out[10] = 2 * nf;
        out[11] = 0;
        out[12] = (left + right) * lr;
        out[13] = (top + bottom) * bt;
        out[14] = (far + near) * nf;
        out[15] = 1;
    }

    /**
     * 创建视图矩阵
     */
    createViewMatrix(out, x, y, zoom) {
        // 重置为单位矩阵
        out[0] = zoom; out[1] = 0; out[2] = 0; out[3] = 0;
        out[4] = 0; out[5] = zoom; out[6] = 0; out[7] = 0;
        out[8] = 0; out[9] = 0; out[10] = 1; out[11] = 0;
        out[12] = -x * zoom; out[13] = -y * zoom; out[14] = 0; out[15] = 1;
    }

    /**
     * 设置相机位置
     */
    setCamera(x, y, zoom = 1) {
        this.camera.x = x;
        this.camera.y = y;
        this.camera.zoom = zoom;
        this.updateViewMatrix();
    }

    /**
     * 获取相机位置
     */
    getCamera() {
        return { ...this.camera };
    }

    /**
     * 开始渲染帧
     */
    beginFrame() {
        const gl = this.gl;
        
        // 重置统计
        this.stats.drawCalls = 0;
        this.stats.triangles = 0;
        this.stats.vertices = 0;

        // 清除缓冲
        gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT);
    }

    /**
     * 结束渲染帧
     */
    endFrame() {
        // 刷新WebGL命令
        this.gl.flush();
    }

    /**
     * 处理画布大小调整
     */
    handleResize(width, height) {
        this.canvas.width = width;
        this.canvas.height = height;
        this.camera.width = width;
        this.camera.height = height;

        this.gl.viewport(0, 0, width, height);
        this.updateProjectionMatrix();
    }

    /**
     * 设置清除颜色
     */
    setClearColor(r, g, b, a = 1.0) {
        this.clearColor = [r, g, b, a];
        this.gl.clearColor(r, g, b, a);
    }

    /**
     * 创建缓冲区
     */
    createBuffer(data, usage = this.gl.STATIC_DRAW) {
        const buffer = this.gl.createBuffer();
        this.gl.bindBuffer(this.gl.ARRAY_BUFFER, buffer);
        this.gl.bufferData(this.gl.ARRAY_BUFFER, data, usage);
        return buffer;
    }

    /**
     * 创建索引缓冲区
     */
    createIndexBuffer(data, usage = this.gl.STATIC_DRAW) {
        const buffer = this.gl.createBuffer();
        this.gl.bindBuffer(this.gl.ELEMENT_ARRAY_BUFFER, buffer);
        this.gl.bufferData(this.gl.ELEMENT_ARRAY_BUFFER, data, usage);
        return buffer;
    }

    /**
     * 绑定缓冲区
     */
    bindBuffer(buffer, target = this.gl.ARRAY_BUFFER) {
        this.gl.bindBuffer(target, buffer);
    }

    /**
     * 设置顶点属性
     */
    setVertexAttribute(index, size, type, normalized, stride, offset) {
        this.gl.enableVertexAttribArray(index);
        this.gl.vertexAttribPointer(index, size, type, normalized, stride, offset);
    }

    /**
     * 绘制三角形
     */
    drawTriangles(count, indexType = this.gl.UNSIGNED_SHORT, offset = 0) {
        this.gl.drawElements(this.gl.TRIANGLES, count, indexType, offset);
        this.stats.drawCalls++;
        this.stats.triangles += count / 3;
        this.stats.vertices += count;
    }

    /**
     * 绘制数组
     */
    drawArrays(mode, first, count) {
        this.gl.drawArrays(mode, first, count);
        this.stats.drawCalls++;
        if (mode === this.gl.TRIANGLES) {
            this.stats.triangles += count / 3;
        }
        this.stats.vertices += count;
    }

    /**
     * 获取渲染统计
     */
    getStats() {
        return { ...this.stats };
    }

    /**
     * 启用/禁用深度测试
     */
    setDepthTest(enabled) {
        if (enabled) {
            this.gl.enable(this.gl.DEPTH_TEST);
        } else {
            this.gl.disable(this.gl.DEPTH_TEST);
        }
    }

    /**
     * 启用/禁用混合
     */
    setBlending(enabled) {
        if (enabled) {
            this.gl.enable(this.gl.BLEND);
        } else {
            this.gl.disable(this.gl.BLEND);
        }
    }

    /**
     * 设置混合模式
     */
    setBlendMode(srcFactor, dstFactor) {
        this.gl.blendFunc(srcFactor, dstFactor);
    }

    /**
     * 释放资源
     */
    dispose() {
        if (this.gl) {
            // 清理WebGL资源
            const numTextureUnits = this.gl.getParameter(this.gl.MAX_TEXTURE_IMAGE_UNITS);
            for (let i = 0; i < numTextureUnits; i++) {
                this.gl.activeTexture(this.gl.TEXTURE0 + i);
                this.gl.bindTexture(this.gl.TEXTURE_2D, null);
            }
            
            this.gl.useProgram(null);
            this.gl.bindBuffer(this.gl.ARRAY_BUFFER, null);
            this.gl.bindBuffer(this.gl.ELEMENT_ARRAY_BUFFER, null);
            this.gl.bindFramebuffer(this.gl.FRAMEBUFFER, null);
            this.gl.bindRenderbuffer(this.gl.RENDERBUFFER, null);
            
            this.gl = null;
        }
    }

    /**
     * 检查WebGL错误
     */
    checkError(operation = '') {
        const error = this.gl.getError();
        if (error !== this.gl.NO_ERROR) {
            let errorString = '';
            switch (error) {
                case this.gl.INVALID_ENUM:
                    errorString = 'INVALID_ENUM';
                    break;
                case this.gl.INVALID_VALUE:
                    errorString = 'INVALID_VALUE';
                    break;
                case this.gl.INVALID_OPERATION:
                    errorString = 'INVALID_OPERATION';
                    break;
                case this.gl.OUT_OF_MEMORY:
                    errorString = 'OUT_OF_MEMORY';
                    break;
                case this.gl.CONTEXT_LOST_WEBGL:
                    errorString = 'CONTEXT_LOST_WEBGL';
                    break;
                default:
                    errorString = `未知错误 (${error})`;
            }
            console.error(`WebGL错误 ${operation}: ${errorString}`);
            return false;
        }
        return true;
    }

    /**
     * 获取WebGL信息
     */
    getWebGLInfo() {
        const gl = this.gl;
        return {
            version: gl.getParameter(gl.VERSION),
            shadingLanguageVersion: gl.getParameter(gl.SHADING_LANGUAGE_VERSION),
            vendor: gl.getParameter(gl.VENDOR),
            renderer: gl.getParameter(gl.RENDERER),
            maxTextureSize: gl.getParameter(gl.MAX_TEXTURE_SIZE),
            maxTextureUnits: gl.getParameter(gl.MAX_TEXTURE_IMAGE_UNITS),
            maxVertexAttribs: gl.getParameter(gl.MAX_VERTEX_ATTRIBS),
            maxVaryingVectors: gl.getParameter(gl.MAX_VARYING_VECTORS),
            maxFragmentUniforms: gl.getParameter(gl.MAX_FRAGMENT_UNIFORM_VECTORS),
            maxVertexUniforms: gl.getParameter(gl.MAX_VERTEX_UNIFORM_VECTORS),
            extensions: Object.keys(this.enabledExtensions)
        };
    }
}