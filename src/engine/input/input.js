/**
 * 输入管理器
 * 处理键盘、鼠标和触摸输入
 */
export class InputManager {
    constructor() {
        // 键盘状态
        this.keys = new Map();
        this.prevKeys = new Map();
        
        // 鼠标状态
        this.mouse = {
            x: 0,
            y: 0,
            prevX: 0,
            prevY: 0,
            deltaX: 0,
            deltaY: 0,
            buttons: new Map(),
            prevButtons: new Map(),
            wheel: 0
        };
        
        // 触摸状态
        this.touches = new Map();
        this.prevTouches = new Map();
        
        // 虚拟按键映射
        this.keyMappings = new Map();
        this.setupDefaultMappings();
        
        // 事件监听器
        this.boundHandlers = {};
        
        // 输入配置
        this.config = {
            enableKeyboard: true,
            enableMouse: true,
            enableTouch: true,
            preventDefaultKeys: ['Space', 'ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight']
        };
        
        // 游戏手柄支持
        this.gamepads = new Map();
        this.gamepadSupported = 'getGamepads' in navigator;
        
        console.log('输入管理器创建完成');
    }

    /**
     * 设置默认按键映射
     */
    setupDefaultMappings() {
        // 马里奥游戏标准控制
        this.keyMappings.set('move_left', ['ArrowLeft', 'KeyA']);
        this.keyMappings.set('move_right', ['ArrowRight', 'KeyD']);
        this.keyMappings.set('jump', ['Space', 'ArrowUp', 'KeyW']);
        this.keyMappings.set('run', ['KeyX', 'ShiftLeft']);
        this.keyMappings.set('duck', ['ArrowDown', 'KeyS']);
        this.keyMappings.set('fire', ['KeyZ', 'ControlLeft']);
        this.keyMappings.set('pause', ['KeyP', 'Escape']);
        this.keyMappings.set('select', ['Enter']);
        this.keyMappings.set('back', ['Backspace']);
    }

    /**
     * 初始化输入管理器
     */
    initialize(canvas) {
        this.canvas = canvas;
        
        if (this.config.enableKeyboard) {
            this.setupKeyboardListeners();
        }
        
        if (this.config.enableMouse) {
            this.setupMouseListeners();
        }
        
        if (this.config.enableTouch) {
            this.setupTouchListeners();
        }
        
        if (this.gamepadSupported) {
            this.setupGamepadListeners();
        }
        
        // 阻止页面滚动等默认行为
        this.preventDefaults();
        
        console.log('输入管理器初始化完成');
    }

    /**
     * 设置键盘事件监听器
     */
    setupKeyboardListeners() {
        this.boundHandlers.keydown = (e) => this.onKeyDown(e);
        this.boundHandlers.keyup = (e) => this.onKeyUp(e);
        
        document.addEventListener('keydown', this.boundHandlers.keydown);
        document.addEventListener('keyup', this.boundHandlers.keyup);
    }

    /**
     * 设置鼠标事件监听器
     */
    setupMouseListeners() {
        this.boundHandlers.mousedown = (e) => this.onMouseDown(e);
        this.boundHandlers.mouseup = (e) => this.onMouseUp(e);
        this.boundHandlers.mousemove = (e) => this.onMouseMove(e);
        this.boundHandlers.wheel = (e) => this.onMouseWheel(e);
        this.boundHandlers.contextmenu = (e) => e.preventDefault();
        
        this.canvas.addEventListener('mousedown', this.boundHandlers.mousedown);
        this.canvas.addEventListener('mouseup', this.boundHandlers.mouseup);
        this.canvas.addEventListener('mousemove', this.boundHandlers.mousemove);
        this.canvas.addEventListener('wheel', this.boundHandlers.wheel);
        this.canvas.addEventListener('contextmenu', this.boundHandlers.contextmenu);
    }

    /**
     * 设置触摸事件监听器
     */
    setupTouchListeners() {
        this.boundHandlers.touchstart = (e) => this.onTouchStart(e);
        this.boundHandlers.touchend = (e) => this.onTouchEnd(e);
        this.boundHandlers.touchmove = (e) => this.onTouchMove(e);
        this.boundHandlers.touchcancel = (e) => this.onTouchCancel(e);
        
        this.canvas.addEventListener('touchstart', this.boundHandlers.touchstart, { passive: false });
        this.canvas.addEventListener('touchend', this.boundHandlers.touchend, { passive: false });
        this.canvas.addEventListener('touchmove', this.boundHandlers.touchmove, { passive: false });
        this.canvas.addEventListener('touchcancel', this.boundHandlers.touchcancel, { passive: false });
    }

    /**
     * 设置游戏手柄监听器
     */
    setupGamepadListeners() {
        this.boundHandlers.gamepadconnected = (e) => this.onGamepadConnected(e);
        this.boundHandlers.gamepaddisconnected = (e) => this.onGamepadDisconnected(e);
        
        window.addEventListener('gamepadconnected', this.boundHandlers.gamepadconnected);
        window.addEventListener('gamepaddisconnected', this.boundHandlers.gamepaddisconnected);
    }

    /**
     * 阻止默认行为
     */
    preventDefaults() {
        document.addEventListener('keydown', (e) => {
            if (this.config.preventDefaultKeys.includes(e.code)) {
                e.preventDefault();
            }
        });
        
        // 阻止双击选择文本
        this.canvas.addEventListener('selectstart', (e) => e.preventDefault());
    }

    /**
     * 键盘按下事件
     */
    onKeyDown(e) {
        this.keys.set(e.code, {
            pressed: true,
            justPressed: !this.prevKeys.get(e.code)?.pressed,
            timestamp: performance.now()
        });
    }

    /**
     * 键盘松开事件
     */
    onKeyUp(e) {
        this.keys.set(e.code, {
            pressed: false,
            justReleased: this.prevKeys.get(e.code)?.pressed || false,
            timestamp: performance.now()
        });
    }

    /**
     * 鼠标按下事件
     */
    onMouseDown(e) {
        this.updateMousePosition(e);
        this.mouse.buttons.set(e.button, {
            pressed: true,
            justPressed: !this.mouse.prevButtons.get(e.button)?.pressed,
            timestamp: performance.now()
        });
    }

    /**
     * 鼠标松开事件
     */
    onMouseUp(e) {
        this.updateMousePosition(e);
        this.mouse.buttons.set(e.button, {
            pressed: false,
            justReleased: this.mouse.prevButtons.get(e.button)?.pressed || false,
            timestamp: performance.now()
        });
    }

    /**
     * 鼠标移动事件
     */
    onMouseMove(e) {
        this.updateMousePosition(e);
    }

    /**
     * 鼠标滚轮事件
     */
    onMouseWheel(e) {
        this.mouse.wheel = e.deltaY;
        e.preventDefault();
    }

    /**
     * 更新鼠标位置
     */
    updateMousePosition(e) {
        const rect = this.canvas.getBoundingClientRect();
        const scaleX = this.canvas.width / rect.width;
        const scaleY = this.canvas.height / rect.height;
        
        this.mouse.prevX = this.mouse.x;
        this.mouse.prevY = this.mouse.y;
        this.mouse.x = (e.clientX - rect.left) * scaleX;
        this.mouse.y = (e.clientY - rect.top) * scaleY;
        this.mouse.deltaX = this.mouse.x - this.mouse.prevX;
        this.mouse.deltaY = this.mouse.y - this.mouse.prevY;
    }

    /**
     * 触摸开始事件
     */
    onTouchStart(e) {
        e.preventDefault();
        for (const touch of e.changedTouches) {
            const pos = this.getTouchPosition(touch);
            this.touches.set(touch.identifier, {
                x: pos.x,
                y: pos.y,
                startX: pos.x,
                startY: pos.y,
                pressed: true,
                justPressed: true,
                timestamp: performance.now()
            });
        }
    }

    /**
     * 触摸结束事件
     */
    onTouchEnd(e) {
        e.preventDefault();
        for (const touch of e.changedTouches) {
            const existingTouch = this.touches.get(touch.identifier);
            if (existingTouch) {
                existingTouch.pressed = false;
                existingTouch.justReleased = true;
            }
        }
    }

    /**
     * 触摸移动事件
     */
    onTouchMove(e) {
        e.preventDefault();
        for (const touch of e.changedTouches) {
            const existingTouch = this.touches.get(touch.identifier);
            if (existingTouch) {
                const pos = this.getTouchPosition(touch);
                existingTouch.x = pos.x;
                existingTouch.y = pos.y;
            }
        }
    }

    /**
     * 触摸取消事件
     */
    onTouchCancel(e) {
        this.onTouchEnd(e);
    }

    /**
     * 获取触摸位置
     */
    getTouchPosition(touch) {
        const rect = this.canvas.getBoundingClientRect();
        const scaleX = this.canvas.width / rect.width;
        const scaleY = this.canvas.height / rect.height;
        
        return {
            x: (touch.clientX - rect.left) * scaleX,
            y: (touch.clientY - rect.top) * scaleY
        };
    }

    /**
     * 游戏手柄连接事件
     */
    onGamepadConnected(e) {
        this.gamepads.set(e.gamepad.index, {
            gamepad: e.gamepad,
            buttons: new Array(e.gamepad.buttons.length).fill(false),
            prevButtons: new Array(e.gamepad.buttons.length).fill(false),
            axes: [...e.gamepad.axes]
        });
        
        console.log(`游戏手柄已连接: ${e.gamepad.id}`);
    }

    /**
     * 游戏手柄断开事件
     */
    onGamepadDisconnected(e) {
        this.gamepads.delete(e.gamepad.index);
        console.log(`游戏手柄已断开: ${e.gamepad.id}`);
    }

    /**
     * 更新输入状态（每帧调用）
     */
    update(deltaTime) {
        // 更新键盘状态
        this.updateKeyboardState();
        
        // 更新鼠标状态
        this.updateMouseState();
        
        // 更新触摸状态
        this.updateTouchState();
        
        // 更新游戏手柄状态
        if (this.gamepadSupported) {
            this.updateGamepadState();
        }
        
        // 清除滚轮值
        this.mouse.wheel = 0;
    }

    /**
     * 更新键盘状态
     */
    updateKeyboardState() {
        // 清除justPressed和justReleased标志
        for (const [code, state] of this.keys) {
            if (state.justPressed) {
                state.justPressed = false;
            }
            if (state.justReleased) {
                state.justReleased = false;
                if (!state.pressed) {
                    this.keys.delete(code);
                }
            }
        }
        
        // 保存前一帧状态
        this.prevKeys.clear();
        for (const [code, state] of this.keys) {
            this.prevKeys.set(code, { ...state });
        }
    }

    /**
     * 更新鼠标状态
     */
    updateMouseState() {
        // 更新按钮状态
        for (const [button, state] of this.mouse.buttons) {
            if (state.justPressed) {
                state.justPressed = false;
            }
            if (state.justReleased) {
                state.justReleased = false;
                if (!state.pressed) {
                    this.mouse.buttons.delete(button);
                }
            }
        }
        
        // 保存前一帧按钮状态
        this.mouse.prevButtons.clear();
        for (const [button, state] of this.mouse.buttons) {
            this.mouse.prevButtons.set(button, { ...state });
        }
        
        // 重置delta
        this.mouse.deltaX = 0;
        this.mouse.deltaY = 0;
    }

    /**
     * 更新触摸状态
     */
    updateTouchState() {
        // 清理已结束的触摸
        for (const [id, touch] of this.touches) {
            if (touch.justPressed) {
                touch.justPressed = false;
            }
            if (touch.justReleased) {
                this.touches.delete(id);
            }
        }
        
        // 保存前一帧状态
        this.prevTouches.clear();
        for (const [id, touch] of this.touches) {
            this.prevTouches.set(id, { ...touch });
        }
    }

    /**
     * 更新游戏手柄状态
     */
    updateGamepadState() {
        const gamepads = navigator.getGamepads();
        
        for (let i = 0; i < gamepads.length; i++) {
            const gamepad = gamepads[i];
            if (!gamepad) continue;
            
            const gamepadState = this.gamepads.get(i);
            if (!gamepadState) continue;
            
            // 更新按钮状态
            for (let j = 0; j < gamepad.buttons.length; j++) {
                gamepadState.prevButtons[j] = gamepadState.buttons[j];
                gamepadState.buttons[j] = gamepad.buttons[j].pressed;
            }
            
            // 更新摇杆状态
            gamepadState.axes = [...gamepad.axes];
        }
    }

    /**
     * 检查按键是否被按住
     */
    isKeyPressed(keyCode) {
        const state = this.keys.get(keyCode);
        return state ? state.pressed : false;
    }

    /**
     * 检查按键是否刚被按下
     */
    isKeyJustPressed(keyCode) {
        const state = this.keys.get(keyCode);
        return state ? state.justPressed : false;
    }

    /**
     * 检查按键是否刚被松开
     */
    isKeyJustReleased(keyCode) {
        const state = this.keys.get(keyCode);
        return state ? state.justReleased : false;
    }

    /**
     * 检查虚拟按键是否被按住
     */
    isActionPressed(action) {
        const keys = this.keyMappings.get(action);
        if (!keys) return false;
        
        return keys.some(key => this.isKeyPressed(key));
    }

    /**
     * 检查虚拟按键是否刚被按下
     */
    isActionJustPressed(action) {
        const keys = this.keyMappings.get(action);
        if (!keys) return false;
        
        return keys.some(key => this.isKeyJustPressed(key));
    }

    /**
     * 检查虚拟按键是否刚被松开
     */
    isActionJustReleased(action) {
        const keys = this.keyMappings.get(action);
        if (!keys) return false;
        
        return keys.some(key => this.isKeyJustReleased(key));
    }

    /**
     * 获取鼠标位置
     */
    getMousePosition() {
        return { x: this.mouse.x, y: this.mouse.y };
    }

    /**
     * 获取鼠标增量
     */
    getMouseDelta() {
        return { x: this.mouse.deltaX, y: this.mouse.deltaY };
    }

    /**
     * 检查鼠标按钮是否被按住
     */
    isMouseButtonPressed(button) {
        const state = this.mouse.buttons.get(button);
        return state ? state.pressed : false;
    }

    /**
     * 检查鼠标按钮是否刚被按下
     */
    isMouseButtonJustPressed(button) {
        const state = this.mouse.buttons.get(button);
        return state ? state.justPressed : false;
    }

    /**
     * 获取滚轮增量
     */
    getMouseWheel() {
        return this.mouse.wheel;
    }

    /**
     * 获取触摸点信息
     */
    getTouches() {
        return Array.from(this.touches.values());
    }

    /**
     * 获取游戏手柄状态
     */
    getGamepadState(index) {
        return this.gamepads.get(index);
    }

    /**
     * 检查游戏手柄按钮是否被按住
     */
    isGamepadButtonPressed(gamepadIndex, buttonIndex) {
        const gamepad = this.gamepads.get(gamepadIndex);
        return gamepad ? gamepad.buttons[buttonIndex] : false;
    }

    /**
     * 检查游戏手柄按钮是否刚被按下
     */
    isGamepadButtonJustPressed(gamepadIndex, buttonIndex) {
        const gamepad = this.gamepads.get(gamepadIndex);
        if (!gamepad) return false;
        
        return gamepad.buttons[buttonIndex] && !gamepad.prevButtons[buttonIndex];
    }

    /**
     * 获取游戏手柄摇杆值
     */
    getGamepadAxis(gamepadIndex, axisIndex) {
        const gamepad = this.gamepads.get(gamepadIndex);
        return gamepad ? gamepad.axes[axisIndex] : 0;
    }

    /**
     * 添加按键映射
     */
    addKeyMapping(action, keys) {
        this.keyMappings.set(action, Array.isArray(keys) ? keys : [keys]);
    }

    /**
     * 移除按键映射
     */
    removeKeyMapping(action) {
        this.keyMappings.delete(action);
    }

    /**
     * 获取所有按键映射
     */
    getKeyMappings() {
        return new Map(this.keyMappings);
    }

    /**
     * 启用/禁用输入类型
     */
    setInputEnabled(type, enabled) {
        this.config[`enable${type.charAt(0).toUpperCase() + type.slice(1)}`] = enabled;
    }

    /**
     * 释放资源
     */
    dispose() {
        // 移除键盘事件监听器
        if (this.boundHandlers.keydown) {
            document.removeEventListener('keydown', this.boundHandlers.keydown);
            document.removeEventListener('keyup', this.boundHandlers.keyup);
        }
        
        // 移除鼠标事件监听器
        if (this.canvas && this.boundHandlers.mousedown) {
            this.canvas.removeEventListener('mousedown', this.boundHandlers.mousedown);
            this.canvas.removeEventListener('mouseup', this.boundHandlers.mouseup);
            this.canvas.removeEventListener('mousemove', this.boundHandlers.mousemove);
            this.canvas.removeEventListener('wheel', this.boundHandlers.wheel);
            this.canvas.removeEventListener('contextmenu', this.boundHandlers.contextmenu);
        }
        
        // 移除触摸事件监听器
        if (this.canvas && this.boundHandlers.touchstart) {
            this.canvas.removeEventListener('touchstart', this.boundHandlers.touchstart);
            this.canvas.removeEventListener('touchend', this.boundHandlers.touchend);
            this.canvas.removeEventListener('touchmove', this.boundHandlers.touchmove);
            this.canvas.removeEventListener('touchcancel', this.boundHandlers.touchcancel);
        }
        
        // 移除游戏手柄事件监听器
        if (this.boundHandlers.gamepadconnected) {
            window.removeEventListener('gamepadconnected', this.boundHandlers.gamepadconnected);
            window.removeEventListener('gamepaddisconnected', this.boundHandlers.gamepaddisconnected);
        }
        
        // 清理状态
        this.keys.clear();
        this.prevKeys.clear();
        this.mouse.buttons.clear();
        this.mouse.prevButtons.clear();
        this.touches.clear();
        this.prevTouches.clear();
        this.gamepads.clear();
        this.keyMappings.clear();
        
        console.log('输入管理器已释放资源');
    }
}