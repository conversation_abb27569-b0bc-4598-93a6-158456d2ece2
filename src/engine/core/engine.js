/**
 * 游戏引擎核心类（修复版）
 * 管理游戏循环、系统协调和资源管理
 */
export class GameEngine {
    constructor(config) {
        this.renderer = config.renderer;
        this.shaderManager = config.shaderManager;
        this.textureManager = config.textureManager;
        this.physicsSystem = config.physicsSystem;
        this.inputManager = config.inputManager;
        this.audioManager = config.audioManager;
        this.canvas = config.canvas;
        
        // 游戏状态
        this.gameState = {
            score: 0,
            coins: 0,
            lives: 3,
            world: '1-1',
            time: 400,
            paused: false,
            gameOver: false,
            levelComplete: false
        };
        
        // 时间管理
        this.clock = {
            startTime: 0,
            lastTime: 0,
            currentTime: 0,
            deltaTime: 0,
            totalTime: 0,
            frameCount: 0,
            fps: 0,
            fpsTimer: 0
        };
        
        // 初始化状态
        this.initializationComplete = false;
        this.initializationTime = 0;
        
        // 游戏对象管理器
        this.mario = null;
        this.enemyManager = null;
        this.itemManager = null;
        this.level = null;
        this.spriteRenderer = null;
        
        // 相机系统
        this.camera = {
            x: 0,
            y: 0,
            targetX: 0,
            targetY: 0,
            smoothing: 0.1,
            bounds: { minX: 0, maxX: 3200, minY: 0, maxY: 480 }
        };
        
        // 关卡数据
        this.levelWidth = 3200;
        this.levelHeight = 480;
        
        // 性能监控
        this.debugMode = false;
        this.renderStats = {
            drawCalls: 0,
            sprites: 0,
            particles: 0
        };
        
        // 事件系统
        this.eventListeners = new Map();
        
        console.log('游戏引擎初始化完成');
    }

    /**
     * 初始化游戏引擎
     */
    async initialize() {
        try {
            // 动态导入所需的类
            const { SpriteRenderer } = await import('../webgl/sprite.js');
            const { Mario } = await import('../../game/entities/mario.js');
            const { EnemyManager } = await import('../../game/entities/enemies.js');
            const { ItemManager } = await import('../../game/entities/items.js');
            const { Level } = await import('../../game/levels/level.js');
            
            // 创建精灵渲染器
            this.spriteRenderer = new SpriteRenderer(
                this.renderer.gl,
                this.shaderManager,
                this.textureManager
            );
            
            // 初始化输入管理器
            this.inputManager.initialize(this.canvas);
            
            // 设置渲染器投影和视图矩阵
            this.updateRenderMatrices();
            
            // 创建游戏对象管理器
            this.enemyManager = new EnemyManager(this);
            this.itemManager = new ItemManager(this);
            this.level = new Level(this);
            
            // 创建马里奥角色（调整到更安全的初始位置）
            this.mario = new Mario(100, 350, this);
            
            console.log(`马里奥创建在位置: (${this.mario.x}, ${this.mario.y}), 地面高度: ${this.levelHeight}`);
            
            // 加载初始关卡
            await this.level.loadLevel('1-1');
            
            // 设置相机初始位置
            this.camera.x = 0;
            this.camera.y = 0;
            this.camera.targetX = 0;
            this.camera.targetY = 0;
            
            // 初始化音频系统
            await this.audioManager.initialize();
            
            // 播放背景音乐
            this.audioManager.playMusic('overworld', true);
            
            // 开始游戏时间
            this.clock.startTime = performance.now();
            
            // 标记初始化完成
            this.initializationComplete = true;
            
            console.log('游戏引擎初始化完成，准备开始游戏');
            
        } catch (error) {
            console.error('游戏引擎初始化失败:', error);
            throw error;
        }
    }


    /**
     * 游戏主循环更新
     */
    update(deltaTime) {
        if (this.gameState.paused || this.gameState.gameOver) {
            return;
        }
        
        // 更新初始化时间计数
        if (this.initializationComplete) {
            this.initializationTime += deltaTime;
        }
        
        // 更新时间
        this.updateClock(deltaTime);
        
        // 更新游戏逻辑时间
        this.gameState.time -= deltaTime;
        if (this.gameState.time <= 0) {
            this.gameState.time = 0;
            this.onTimeUp();
        }
        
        // 更新输入
        this.inputManager.update(deltaTime);
        
        // 更新马里奥
        if (this.mario) {
            this.mario.update(deltaTime);
        }
        
        // 更新敌人
        if (this.enemyManager) {
            this.enemyManager.update(deltaTime);
        }
        
        // 更新道具
        if (this.itemManager) {
            this.itemManager.update(deltaTime);
        }
        
        // 更新关卡
        if (this.level) {
            this.level.update(deltaTime);
        }
        
        // 更新相机
        this.updateCamera(deltaTime);
        
        // 更新物理系统
        this.physicsSystem.update(deltaTime);
        
        // 检查游戏结束条件
        this.checkGameEndConditions();
    }

    /**
     * 更新时钟
     */
    updateClock(deltaTime) {
        this.clock.deltaTime = deltaTime;
        this.clock.totalTime += deltaTime;
        this.clock.frameCount++;
        
        // 计算FPS
        this.clock.fpsTimer += deltaTime;
        if (this.clock.fpsTimer >= 1.0) {
            this.clock.fps = Math.round(this.clock.frameCount / this.clock.fpsTimer);
            this.clock.frameCount = 0;
            this.clock.fpsTimer = 0;
        }
    }


    /**
     * 更新相机
     */
    updateCamera(deltaTime) {
        if (!this.mario) return;
        
        const camera = this.camera;
        
        // 设置相机目标位置（跟随马里奥）
        camera.targetX = this.mario.x - this.canvas.width / 2;
        camera.targetY = 0; // 2D横版游戏通常Y轴固定
        
        // 平滑跟随
        const dx = camera.targetX - camera.x;
        const dy = camera.targetY - camera.y;
        
        camera.x += dx * camera.smoothing;
        camera.y += dy * camera.smoothing;
        
        // 应用边界限制
        camera.x = Math.max(camera.bounds.minX, 
                   Math.min(camera.x, camera.bounds.maxX - this.canvas.width));
        camera.y = Math.max(camera.bounds.minY,
                   Math.min(camera.y, camera.bounds.maxY - this.canvas.height));
        
        // 更新渲染器相机
        this.renderer.setCamera(camera.x, camera.y, 1.0);
    }

    /**
     * 渲染游戏
     */
    render() {
        // 开始渲染帧
        this.renderer.beginFrame();
        
        // 更新渲染矩阵（视图和投影）
        this.updateRenderMatrices();
        
        // 重置统计
        this.renderStats.sprites = 0;
        this.renderStats.drawCalls = 0;
        
        // 开始精灵批次
        this.spriteRenderer.begin();
        
        // 渲染关卡
        if (this.level) {
            this.level.render(this.spriteRenderer);
        }
        
        // 渲染道具
        if (this.itemManager) {
            this.itemManager.render(this.spriteRenderer);
        }
        
        // 渲染敌人
        if (this.enemyManager) {
            this.enemyManager.render(this.spriteRenderer);
        }
        
        // 渲染马里奥
        if (this.mario) {
            this.mario.render(this.spriteRenderer);
        }
        
        // 渲染调试信息
        if (this.debugMode) {
            this.renderDebugInfo();
        }
        
        // 结束精灵批次
        this.spriteRenderer.end();
        
        // 结束渲染帧
        this.renderer.endFrame();
        
        // 更新统计
        const stats = this.renderer.getStats();
        this.renderStats.drawCalls = stats.drawCalls;
    }


    /**
     * 渲染调试信息
     */
    renderDebugInfo() {
        // 渲染马里奥的碰撞框
        if (this.mario && this.mario.physicsBody) {
            this.spriteRenderer.drawDebugRect(
                this.mario.physicsBody.x,
                this.mario.physicsBody.y,
                this.mario.physicsBody.width,
                this.mario.physicsBody.height,
                [1, 0, 0, 0.5] // 红色半透明
            );
        }
        
        // 渲染敌人碰撞框
        if (this.enemyManager) {
            for (const enemy of this.enemyManager.getAllEnemies()) {
                if (enemy.physicsBody) {
                    this.spriteRenderer.drawDebugRect(
                        enemy.physicsBody.x,
                        enemy.physicsBody.y,
                        enemy.physicsBody.width,
                        enemy.physicsBody.height,
                        [1, 1, 0, 0.3] // 黄色半透明
                    );
                }
            }
        }
    }

    /**
     * 更新渲染矩阵
     */
    updateRenderMatrices() {
        // 设置投影和视图矩阵的uniform
        const projMatrix = this.renderer.projectionMatrix;
        const viewMatrix = this.renderer.viewMatrix;
        
        // 只在首次调用时输出调试信息
        if (!this.matricesDebugLogged) {
            console.log('相机信息:', this.renderer.getCamera());
            console.log('投影矩阵:', projMatrix.slice(0, 4), '...'); 
            console.log('视图矩阵:', viewMatrix.slice(0, 4), '...');
            this.matricesDebugLogged = true;
        }
        
        // 更新所有着色器的矩阵
        const programs = this.shaderManager.getProgramNames();
        for (const programName of programs) {
            if (this.shaderManager.hasProgram(programName)) {
                try {
                    this.shaderManager.setUniform(programName, 'u_projection', projMatrix);
                    this.shaderManager.setUniform(programName, 'u_view', viewMatrix);
                } catch (error) {
                    // 某些着色器可能没有这些uniform，忽略错误
                }
            }
        }
    }

    /**
     * 检查游戏结束条件
     */
    checkGameEndConditions() {
        if (!this.mario) return;
        
        // 给物理系统一些时间稳定（前0.5秒不检查死亡）
        if (!this.initializationComplete || this.initializationTime < 0.5) {
            return;
        }
        
        // 检查马里奥是否掉落
        if (this.mario.y > this.levelHeight) {
            this.onMarioDied();
        }
        
        // 检查是否到达关卡终点
        if (this.mario.x >= this.levelWidth - 100) {
            this.onLevelComplete();
        }
    }

    /**
     * 马里奥死亡处理
     */
    onMarioDied() {
        this.gameState.lives--;
        
        if (this.gameState.lives <= 0) {
            this.gameState.gameOver = true;
            this.emit('gameOver');
        } else {
            this.restartLevel();
        }
    }

    /**
     * 关卡完成处理
     */
    onLevelComplete() {
        this.gameState.levelComplete = true;
        this.gameState.score += Math.floor(this.gameState.time) * 50;
        this.emit('levelComplete');
    }

    /**
     * 时间用尽处理
     */
    onTimeUp() {
        this.onMarioDied();
    }

    /**
     * 重启关卡
     */
    restartLevel() {
        // 重置马里奥位置和状态（使用更安全的位置）
        if (this.mario) {
            this.mario.x = 100;
            this.mario.y = 350;
            this.mario.velocityX = 0;
            this.mario.velocityY = 0;
            this.mario.onGround = false;
            this.mario.state = 'small';
            
            // 同步物理体位置
            if (this.mario.physicsBody) {
                this.mario.physicsBody.x = 100;
                this.mario.physicsBody.y = 350;
                this.mario.physicsBody.velocityX = 0;
                this.mario.physicsBody.velocityY = 0;
            }
        }
        
        // 重置相机
        this.camera.x = 0;
        this.camera.targetX = 0;
        
        // 重置时间
        this.gameState.time = 400;
        
        // 重置初始化时间，给物理系统重新稳定的时间
        this.initializationTime = 0;
        
        console.log('关卡重启');
    }

    /**
     * 暂停/恢复游戏
     */
    togglePause() {
        this.gameState.paused = !this.gameState.paused;
        this.emit('pauseToggle', this.gameState.paused);
    }

    /**
     * 暂停游戏
     */
    pause() {
        this.gameState.paused = true;
        this.emit('pause');
    }

    /**
     * 恢复游戏
     */
    resume() {
        this.gameState.paused = false;
        this.emit('resume');
    }

    /**
     * 检查是否暂停
     */
    isPaused() {
        return this.gameState.paused;
    }

    /**
     * 重启游戏
     */
    restart() {
        // 重置游戏状态
        this.gameState.score = 0;
        this.gameState.coins = 0;
        this.gameState.lives = 3;
        this.gameState.time = 400;
        this.gameState.paused = false;
        this.gameState.gameOver = false;
        this.gameState.levelComplete = false;
        
        // 重启关卡
        this.restartLevel();
        
        this.emit('restart');
        console.log('游戏重启');
    }

    /**
     * 处理窗口大小调整
     */
    handleResize() {
        const rect = this.canvas.getBoundingClientRect();
        this.canvas.width = rect.width;
        this.canvas.height = rect.height;
        
        this.renderer.handleResize(rect.width, rect.height);
        this.updateRenderMatrices();
        
        console.log(`画布大小调整: ${rect.width}x${rect.height}`);
    }

    /**
     * 获取游戏状态
     */
    getGameState() {
        return { ...this.gameState };
    }

    /**
     * 获取性能统计
     */
    getPerformanceStats() {
        return {
            fps: this.clock.fps,
            frameTime: this.clock.deltaTime * 1000,
            totalTime: this.clock.totalTime,
            renderStats: { ...this.renderStats },
            entities: this.entities.size
        };
    }

    /**
     * 开启/关闭调试模式
     */
    toggleDebugMode() {
        this.debugMode = !this.debugMode;
        console.log(`调试模式: ${this.debugMode ? '开启' : '关闭'}`);
    }

    /**
     * 事件系统
     */
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    emit(event, ...args) {
        const listeners = this.eventListeners.get(event);
        if (listeners) {
            for (const callback of listeners) {
                try {
                    callback(...args);
                } catch (error) {
                    console.error(`事件监听器错误 (${event}):`, error);
                }
            }
        }
    }

    /**
     * 释放引擎资源
     */
    dispose() {
        // 停止游戏循环
        this.gameState.gameOver = true;
        
        // 释放渲染资源
        if (this.renderer) {
            this.renderer.dispose();
        }
        
        if (this.shaderManager) {
            this.shaderManager.dispose();
        }
        
        if (this.textureManager) {
            this.textureManager.dispose();
        }
        
        // 释放输入管理器
        if (this.inputManager) {
            this.inputManager.dispose();
        }
        
        // 释放音频管理器
        if (this.audioManager) {
            this.audioManager.dispose();
        }
        
        // 释放游戏对象
        if (this.mario) {
            this.mario.dispose();
            this.mario = null;
        }
        
        if (this.enemyManager) {
            this.enemyManager.dispose();
            this.enemyManager = null;
        }
        
        if (this.itemManager) {
            this.itemManager.dispose();
            this.itemManager = null;
        }
        
        if (this.level) {
            this.level.dispose();
            this.level = null;
        }
        
        if (this.spriteRenderer) {
            this.spriteRenderer.dispose();
            this.spriteRenderer = null;
        }
        
        // 清理事件监听器
        this.eventListeners.clear();
        
        console.log('游戏引擎已释放所有资源');
    }
}