# CRUSH Configuration

## Build, Lint, and Test Commands

*   **Build:** (No specific build command found, assuming standard JS/HTML project)
*   **Lint:** `npm run lint` (if available)
*   **Test:** `npm test` (if available)
*   **Run Single Test:** `npm test <test_file_or_description>` (if supported)

## Code Style Guidelines

*   **Imports:** Organize imports alphabetically.
*   **Formatting:** Adhere to common JavaScript/HTML formatting standards (e.g., <PERSON><PERSON><PERSON>).
*   **Types:** Use TypeScript for type safety if applicable. If not, use JSDoc for type annotations.
*   **Naming Conventions:**
    *   Variables and functions: camelCase
    *   Classes and components: PascalCase
    *   Constants: UPPER_SNAKE_CASE
*   **Error Handling:** Use try-catch blocks for synchronous operations and `.catch()` for Promises. Provide meaningful error messages.

## Other Configurations

*   **Cursor Rules:** None found.
*   **Copilot Instructions:** None found.

## .gitignore

Ensure `.crush` directory is added to `.gitignore` if it's not already present.

## Notes for Agents

*   This project appears to be a client-side web application using WebGL.
*   Focus on front-end development practices.
*   Refer to `mario-webgl.html` for the main entry point.
*   `src/` directory likely contains engine and game logic.