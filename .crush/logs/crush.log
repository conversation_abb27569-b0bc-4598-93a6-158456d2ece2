{"time":"2025-08-11T17:26:38.807482+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":99},"msg":"Using cached provider data","path":"/Users/<USER>/.local/share/crush/providers.json"}
{"time":"2025-08-11T17:26:38.809958+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders.func1","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":103},"msg":"Updating provider cache in background"}
{"time":"2025-08-11T17:26:39.366732+08:00","level":"INFO","msg":"OK   20250424200609_initial.sql (914.13µs)"}
{"time":"2025-08-11T17:26:39.367047+08:00","level":"INFO","msg":"OK   20250515105448_add_summary_message_id.sql (289.54µs)"}
{"time":"2025-08-11T17:26:39.367366+08:00","level":"INFO","msg":"OK   20250624000000_add_created_at_indexes.sql (253.04µs)"}
{"time":"2025-08-11T17:26:39.367676+08:00","level":"INFO","msg":"OK   20250627000000_add_provider_to_messages.sql (296.88µs)"}
{"time":"2025-08-11T17:26:39.367698+08:00","level":"INFO","msg":"goose: successfully migrated database to version: 20250627000000"}
{"time":"2025-08-11T17:26:39.368728+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/app.(*App).initLSPClients","file":"/home/<USER>/work/crush/crush/internal/app/lsp.go","line":18},"msg":"LSP clients initialization started in background"}
{"time":"2025-08-11T17:26:39.375273+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initializing agent tools","agent":"task"}
{"time":"2025-08-11T17:26:39.375864+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initializing agent tools","agent":"coder"}
{"time":"2025-08-11T17:26:39.375871+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":175},"msg":"Initialized agent tools","agent":"task"}
{"time":"2025-08-11T17:26:39.375891+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":175},"msg":"Initialized agent tools","agent":"coder"}
{"time":"2025-08-11T17:26:40.184717+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.saveProvidersInCache","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":48},"msg":"Saving cached provider data","path":"/Users/<USER>/.local/share/crush/providers.json"}
{"time":"2025-08-11T17:26:53.392716+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":99},"msg":"Using cached provider data","path":"/Users/<USER>/.local/share/crush/providers.json"}
{"time":"2025-08-11T17:26:53.394123+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders.func1","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":103},"msg":"Updating provider cache in background"}
{"time":"2025-08-11T17:26:53.920159+08:00","level":"INFO","msg":"goose: no migrations to run. current version: 20250627000000"}
{"time":"2025-08-11T17:26:53.920207+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/app.(*App).initLSPClients","file":"/home/<USER>/work/crush/crush/internal/app/lsp.go","line":18},"msg":"LSP clients initialization started in background"}
{"time":"2025-08-11T17:26:53.923472+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initializing agent tools","agent":"task"}
{"time":"2025-08-11T17:26:53.923537+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":175},"msg":"Initialized agent tools","agent":"task"}
{"time":"2025-08-11T17:26:53.924055+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initializing agent tools","agent":"coder"}
{"time":"2025-08-11T17:26:53.924083+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":175},"msg":"Initialized agent tools","agent":"coder"}
{"time":"2025-08-11T17:26:54.202276+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.saveProvidersInCache","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":48},"msg":"Saving cached provider data","path":"/Users/<USER>/.local/share/crush/providers.json"}
{"time":"2025-08-11T17:26:54.567354+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initializing agent tools","agent":"task"}
{"time":"2025-08-11T17:26:54.567486+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":175},"msg":"Initialized agent tools","agent":"task"}
{"time":"2025-08-11T17:26:54.568945+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initializing agent tools","agent":"coder"}
{"time":"2025-08-11T17:26:54.569025+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":175},"msg":"Initialized agent tools","agent":"coder"}
{"time":"2025-08-11T17:26:56.474941+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"tool_0_bash","name":"bash","input":"","type":"","finished":false}}
{"time":"2025-08-11T17:26:56.498206+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/shell.(*loggingAdapter).InfoPersist","file":"/home/<USER>/work/crush/crush/internal/shell/persistent.go","line":36},"msg":"POSIX command finished","command":"ls -a","err":null}
{"time":"2025-08-11T17:26:57.6148+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"tool_0_bash","name":"bash","input":"","type":"","finished":false}}
{"time":"2025-08-11T17:26:57.61642+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/shell.(*loggingAdapter).InfoPersist","file":"/home/<USER>/work/crush/crush/internal/shell/persistent.go","line":36},"msg":"POSIX command finished","command":"ls -a .crush","err":null}
{"time":"2025-08-11T17:26:58.815269+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"tool_0_bash","name":"bash","input":"","type":"","finished":false}}
{"time":"2025-08-11T17:26:58.816829+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/shell.(*loggingAdapter).InfoPersist","file":"/home/<USER>/work/crush/crush/internal/shell/persistent.go","line":36},"msg":"POSIX command finished","command":"ls -a .github","err":null}
{"time":"2025-08-11T17:27:00.035515+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"tool_0_bash","name":"bash","input":"","type":"","finished":false}}
{"time":"2025-08-11T17:27:00.039365+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/shell.(*loggingAdapter).InfoPersist","file":"/home/<USER>/work/crush/crush/internal/shell/persistent.go","line":36},"msg":"POSIX command finished","command":"ls -a .cursor","err":null}
{"time":"2025-08-11T17:27:02.526046+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"tool_0_write","name":"write","input":"","type":"","finished":false}}
{"time":"2025-08-11T17:27:06.934052+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"tool_0_bash","name":"bash","input":"","type":"","finished":false}}
{"time":"2025-08-11T17:27:08.941856+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/shell.(*loggingAdapter).InfoPersist","file":"/home/<USER>/work/crush/crush/internal/shell/persistent.go","line":36},"msg":"POSIX command finished","command":"grep \"javascript\" -r --include=*.js --include=*.jsx --include=*.ts --include=*.tsx --include=*.mjs --include=*.cjs","err":"exit status 1"}
{"time":"2025-08-11T17:27:10.47818+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"tool_0_bash","name":"bash","input":"","type":"","finished":false}}
{"time":"2025-08-11T17:27:10.488623+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/shell.(*loggingAdapter).InfoPersist","file":"/home/<USER>/work/crush/crush/internal/shell/persistent.go","line":36},"msg":"POSIX command finished","command":"ls -a src/engine/","err":null}
{"time":"2025-08-11T17:27:20.682767+08:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).Run.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":347},"msg":"failed to process events: POST \"https://openrouter.ai/api/v1/chat/completions\": 402 Payment Required {\"message\":\"This request requires more credits, or fewer max_tokens. You requested up to 32000 tokens, but can only afford 2318. To increase, visit https://openrouter.ai/settings/credits and upgrade to a paid account\",\"code\":402,\"metadata\":{\"provider_name\":null}}"}
