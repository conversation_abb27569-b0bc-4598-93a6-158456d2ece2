<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Super Mario WebGL - 超级马里奥</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Press Start 2P', monospace;
            background: #222;
            color: white;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        #gameContainer {
            position: relative;
            border: 4px solid #444;
            background: #000;
            box-shadow: 0 0 20px rgba(0,255,0,0.3);
        }

        #gameCanvas {
            display: block;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
            background: linear-gradient(180deg, #5C94FC 0%, #87CEEB 100%);
        }

        #gameUI {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 40px;
            background: #000;
            border-bottom: 2px solid #444;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            z-index: 10;
        }

        .ui-section {
            display: flex;
            gap: 20px;
            font-size: 12px;
            color: white;
        }

        .ui-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 2px;
        }

        .ui-label {
            font-size: 8px;
            color: #888;
        }

        .ui-value {
            color: white;
        }

        #loadingScreen {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: #000;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            gap: 20px;
            z-index: 1000;
        }

        .loading-title {
            font-size: 24px;
            color: #FFD700;
            text-shadow: 2px 2px 0 #B8860B;
            animation: pulse 2s infinite;
        }

        .loading-bar {
            width: 300px;
            height: 20px;
            border: 2px solid #444;
            background: #222;
            position: relative;
            overflow: hidden;
        }

        .loading-fill {
            height: 100%;
            background: linear-gradient(90deg, #FF6B6B, #4ECDC4, #45B7D1, #96CEB4);
            width: 0%;
            transition: width 0.3s ease;
            position: relative;
        }

        .loading-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: shimmer 1.5s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .controls {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.8);
            padding: 15px;
            border-radius: 8px;
            font-size: 8px;
            text-align: center;
            display: none;
        }

        .controls.show {
            display: block;
        }

        .control-row {
            margin-bottom: 5px;
        }

        .error-display {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #ff4444;
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            display: none;
            z-index: 2000;
        }

        .error-display.show {
            display: block;
            animation: shake 0.5s;
        }

        @keyframes shake {
            0%, 100% { transform: translate(-50%, -50%); }
            25% { transform: translate(-55%, -50%); }
            75% { transform: translate(-45%, -50%); }
        }

        .fps-counter {
            position: absolute;
            top: 10px;
            right: 10px;
            font-size: 8px;
            color: #0f0;
            background: rgba(0,0,0,0.5);
            padding: 5px;
            border-radius: 4px;
            z-index: 100;
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <!-- 游戏UI -->
        <div id="gameUI">
            <div class="ui-section">
                <div class="ui-item">
                    <div class="ui-label">MARIO</div>
                    <div class="ui-value" id="score">000000</div>
                </div>
                <div class="ui-item">
                    <div class="ui-label">COIN</div>
                    <div class="ui-value" id="coins">00</div>
                </div>
            </div>
            <div class="ui-section">
                <div class="ui-item">
                    <div class="ui-label">WORLD</div>
                    <div class="ui-value" id="world">1-1</div>
                </div>
                <div class="ui-item">
                    <div class="ui-label">TIME</div>
                    <div class="ui-value" id="time">400</div>
                </div>
            </div>
            <div class="ui-section">
                <div class="ui-item">
                    <div class="ui-label">LIVES</div>
                    <div class="ui-value" id="lives">3</div>
                </div>
            </div>
        </div>

        <!-- WebGL画布 -->
        <canvas id="gameCanvas" width="800" height="600"></canvas>
        
        <!-- FPS计数器 -->
        <div class="fps-counter" id="fpsCounter">FPS: 60</div>

        <!-- 加载屏幕 -->
        <div id="loadingScreen">
            <div class="loading-title">SUPER MARIO</div>
            <div class="loading-bar">
                <div class="loading-fill" id="loadingFill"></div>
            </div>
            <div style="font-size: 10px; color: #888; margin-top: 10px;">
                正在加载游戏资源...
            </div>
        </div>

        <!-- 控制说明 -->
        <div class="controls" id="controlsDisplay">
            <div class="control-row">方向键: 移动 | 空格: 跳跃</div>
            <div class="control-row">X: 奔跑/发射 | Z: 蹲下</div>
            <div class="control-row">P: 暂停 | R: 重启</div>
        </div>

        <!-- 错误显示 -->
        <div class="error-display" id="errorDisplay">
            <div id="errorMessage"></div>
            <button onclick="hideError()" style="margin-top: 10px; padding: 5px 15px;">确定</button>
        </div>
    </div>

    <!-- 游戏脚本模块 -->
    <script type="module">
        // WebGL引擎核心
        import { WebGLRenderer } from './src/engine/webgl/renderer.js';
        import { ShaderManager } from './src/engine/webgl/shader.js';
        import { TextureManager } from './src/engine/webgl/texture.js';
        import { SpriteRenderer } from './src/engine/webgl/sprite.js';
        
        // 游戏引擎系统
        import { GameEngine } from './src/engine/core/engine.js';
        import { PhysicsSystem } from './src/engine/physics/physics.js';
        import { InputManager } from './src/engine/input/input.js';
        import { AudioManager } from './src/engine/audio/audio.js';
        
        // 游戏对象
        import { Mario } from './src/game/entities/mario.js';
        import { EnemyManager } from './src/game/entities/enemies.js';
        import { ItemManager } from './src/game/entities/items.js';
        import { Level } from './src/game/levels/level.js';
        
        // 全局游戏实例
        let game = null;
        let isGameRunning = false;
        
        // UI元素引用
        const canvas = document.getElementById('gameCanvas');
        const loadingScreen = document.getElementById('loadingScreen');
        const loadingFill = document.getElementById('loadingFill');
        const errorDisplay = document.getElementById('errorDisplay');
        const errorMessage = document.getElementById('errorMessage');
        const controlsDisplay = document.getElementById('controlsDisplay');
        const fpsCounter = document.getElementById('fpsCounter');
        
        // UI更新元素
        const scoreElement = document.getElementById('score');
        const coinsElement = document.getElementById('coins');
        const worldElement = document.getElementById('world');
        const timeElement = document.getElementById('time');
        const livesElement = document.getElementById('lives');

        // 初始化游戏
        async function initGame() {
            try {
                showLoading('初始化WebGL上下文...');
                await sleep(500);
                
                // 初始化WebGL渲染器
                const renderer = new WebGLRenderer(canvas);
                if (!renderer.initialize()) {
                    throw new Error('无法初始化WebGL上下文');
                }
                
                showLoading('加载着色器程序...', 20);
                await sleep(300);
                
                // 初始化着色器管理器
                const shaderManager = new ShaderManager(renderer.gl);
                await shaderManager.loadShaders();
                
                showLoading('加载纹理资源...', 40);
                await sleep(500);
                
                // 初始化纹理管理器
                const textureManager = new TextureManager(renderer.gl);
                await textureManager.loadTextures();
                
                showLoading('初始化游戏引擎...', 60);
                await sleep(300);
                
                // 初始化核心系统
                const physicsSystem = new PhysicsSystem();
                const inputManager = new InputManager();
                const audioManager = new AudioManager();
                
                showLoading('创建游戏世界...', 80);
                await sleep(400);
                
                // 创建游戏引擎实例
                game = new GameEngine({
                    renderer,
                    shaderManager,
                    textureManager,
                    physicsSystem,
                    inputManager,
                    audioManager,
                    canvas
                });
                
                showLoading('启动游戏...', 100);
                await sleep(500);
                
                // 初始化游戏内容
                await game.initialize();
                
                // 隐藏加载屏幕
                hideLoading();
                
                // 显示控制说明
                setTimeout(() => {
                    controlsDisplay.classList.add('show');
                    setTimeout(() => {
                        controlsDisplay.classList.remove('show');
                    }, 5000);
                }, 1000);
                
                // 启动游戏循环
                startGameLoop();
                
            } catch (error) {
                console.error('游戏初始化失败:', error);
                showError('游戏初始化失败: ' + error.message);
            }
        }

        // 游戏主循环
        function startGameLoop() {
            if (!game) return;
            
            let lastTime = 0;
            let frameCount = 0;
            let fpsTimer = 0;
            
            function gameLoop(currentTime) {
                // 初始化lastTime，避免第一帧的巨大deltaTime
                if (lastTime === 0) {
                    lastTime = currentTime;
                }
                
                let deltaTime = (currentTime - lastTime) / 1000;
                lastTime = currentTime;
                
                // 限制deltaTime以防止物理爆炸
                deltaTime = Math.min(deltaTime, 1/30); // 最大33ms
                
                // 更新FPS计数
                frameCount++;
                fpsTimer += deltaTime;
                if (fpsTimer >= 1.0) {
                    fpsCounter.textContent = `FPS: ${Math.round(frameCount / fpsTimer)}`;
                    frameCount = 0;
                    fpsTimer = 0;
                }
                
                // 更新游戏逻辑
                if (game && !game.isPaused()) {
                    game.update(deltaTime);
                    game.render();
                    updateUI();
                }
                
                // 继续循环
                if (isGameRunning) {
                    requestAnimationFrame(gameLoop);
                }
            }
            
            isGameRunning = true;
            requestAnimationFrame(gameLoop);
        }

        // 更新UI显示
        function updateUI() {
            if (!game || !game.mario) return;
            
            const gameState = game.getGameState();
            
            scoreElement.textContent = gameState.score.toString().padStart(6, '0');
            coinsElement.textContent = gameState.coins.toString().padStart(2, '0');
            worldElement.textContent = gameState.world;
            timeElement.textContent = Math.max(0, Math.floor(gameState.time)).toString();
            livesElement.textContent = gameState.lives.toString();
        }

        // 显示加载进度
        function showLoading(message, progress = 0) {
            loadingFill.style.width = progress + '%';
            const messageElement = document.querySelector('#loadingScreen div:last-child');
            if (messageElement) {
                messageElement.textContent = message;
            }
        }

        // 隐藏加载屏幕
        function hideLoading() {
            loadingScreen.style.display = 'none';
        }

        // 显示错误信息
        function showError(message) {
            errorMessage.textContent = message;
            errorDisplay.classList.add('show');
        }

        // 隐藏错误信息
        window.hideError = function() {
            errorDisplay.classList.remove('show');
        }

        // 工具函数：延迟
        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // 键盘事件处理
        document.addEventListener('keydown', (e) => {
            if (!game) return;
            
            switch(e.code) {
                case 'KeyP':
                    game.togglePause();
                    break;
                case 'KeyR':
                    if (e.ctrlKey || e.metaKey) return; // 防止刷新页面
                    game.restart();
                    break;
                case 'KeyC':
                    controlsDisplay.classList.toggle('show');
                    break;
            }
        });

        // 页面可见性变化处理
        document.addEventListener('visibilitychange', () => {
            if (game) {
                if (document.hidden) {
                    game.pause();
                } else {
                    // 可以选择自动恢复或保持暂停
                }
            }
        });

        // 窗口大小调整处理
        window.addEventListener('resize', () => {
            if (game) {
                game.handleResize();
            }
        });

        // 错误处理
        window.addEventListener('error', (e) => {
            console.error('游戏运行错误:', e.error);
            showError('游戏运行出错: ' + e.error.message);
        });

        // 启动游戏
        window.addEventListener('load', () => {
            // 检查WebGL支持
            const testCanvas = document.createElement('canvas');
            const testGL = testCanvas.getContext('webgl') || testCanvas.getContext('experimental-webgl');
            
            if (!testGL) {
                showError('您的浏览器不支持WebGL，无法运行游戏');
                return;
            }
            
            // 延迟启动，显示加载动画
            setTimeout(initGame, 1000);
        });

        // 导出全局接口供调试使用
        window.game = () => game;
        window.debugMode = false;
    </script>
</body>
</html>